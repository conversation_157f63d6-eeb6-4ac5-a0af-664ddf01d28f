- if lookup_context.find_all("/specdbi/ms_ir/_show.html.slim").any?
  = render '/specdbi/ms_ir/show.html.slim', spectrum: @spectrum
- else
  .page-header: h1 = title "#{@spectrum.spectrum_type} (#{@spectrum.attributes[:spectrum_type].gsub('IRIS Spectrum - ', '')}, #{@spectrum.adduct}) (#{@spectrum.compound.try(:to_param)})"

  .specdb.col-md-10
    .panel.panel-info#details
      .panel-heading Spectrum Details
      .panel-body
        table.spec-table
          tr
            th #{specdb_config :accessor_database_name} ID:
            td =nah get_compound_id(@spectrum.compound)
          tr
            th Compound name:
            td =nah @spectrum.compound.try(:name)
          tr
            th Spectrum type:
            td =nah @spectrum.spectrum_type
          tr
            th Adduct:
            td =nah @spectrum.adduct

    .panel.panel-info#spectrum
      .panel-heading Spectrum View
      .panel-body
        = render 'spectra_viewer_ir'

    .panel.panel-info#conditions
      .panel-heading Experimental Conditions
      .panel-body
        table.spec-table
          tr
            th #{specdb_config :accessor_database_name} ID:
            td = nah get_compound_id(@spectrum.compound)
          tr
            th Spectrum type:
            td = nah @spectrum.spectrum_type
          tr
            th Adduct:
            td = nah @spectrum.adduct
          tr
            th Instrument Type:
            td = nah @spectrum.instrument
          tr
            th Laser Power:
            td = nah "#{@spectrum.laser_pulse_energy} #{@spectrum.laser_pulse_energy_unit}"
          tr
            th Number of Pulses:
            td = nah @spectrum.number_of_pulses

    .panel.panel-info#documentation
      .panel-heading Documentation
      .panel-body
        - if @spectrum.documents.present?
          table.table.table-condensed.table-striped.unpadded-table
            thead
              tr
                th Document Description
                th Download
                th File Size
            tbody
              - unrestricted_downloads_list(@spectrum).each do |doc|
                tr
                  td = doc[0]
                  - if doc[1].nil?
                    td = nah
                  - else
                    td = link_to "Download file",
                            (doc[1].include?('https') ? doc[1].url : doc[1].gsub('http', 'https')),
                            download: ''
                  td = nah doc[2]

        - else
          .no-results = nah

    .panel.panel-info#references
      .panel-heading References
      .panel-body
        - if @spectrum.references.present?
          ol
            - @spectrum.references.each do |reference|
              - begin
                - if reference.ref_text.present? # Do not want to display HMDB links
                  - if !reference.ref_text.starts_with? 'http' # Do not want to display plain links
                    li
                      | #{reference.ref_text.html_safe}
                      - if reference.pubmed_id.present?
                        | &nbsp;[#{bio_link_out(:pubmed, reference.pubmed_id, "PubMed: #{reference.pubmed_id}")}]
                      - if reference.database.present?
                        - if (reference.database != 'HMDB') && (!reference.database.include? 'MetaboBASE') # MetaboBASE has no link to spectra
                          - if reference.database_id.present?
                            - if reference.ref_text.include? reference.database # Don't want the database link on a separate line if the ref_text refers to it (that's just redundant)
                              | &nbsp;[#{bio_link_out reference.database.downcase.gsub('mona', 'mona_spectrum').gsub('-','_'), reference.database_id}]

                - if reference.database.present? # If no ref link or database is different from ref link
                  - if reference.database.include? 'MetaboBASE'
                    li
                      | Bruker-Sumner MetaboBASE Plant Library
                  - elsif (reference.database != 'HMDB') && ((reference.ref_text.blank?) || (!reference.ref_text.include? reference.database))
                    li
                      | #{bio_link_to_source reference.database.downcase.gsub('-','_')}
                      - if reference.database_id.present?
                        | &nbsp;[#{bio_link_out reference.database.downcase.gsub('mona', 'mona_spectrum').gsub('-','_'), reference.database_id}]
              - rescue
                | Error displaying reference
        - else
          .no-results = nah

  nav.sidenav.specdb-sidenav.col-md-2
    div data-spy="affix" data-offset-top="90"
      ul.nav.nav-pills.nav-stacked
        li.active = link_to 'Details', '#details'
        li = link_to 'Spectrum', '#spectrum'
        li = link_to 'Experimental Conditions', '#conditions'
        li = link_to 'Documentation', '#documentation'
        li = link_to 'References', '#references'
