- jsviewer_link = link_to "View Spectrum", spectrum_path(spectrum), 'data-turbolinks' => false,
        class: 'btn btn-xs btn-primary'
- view_options = jsviewer_link

td = spectrum.spectrum_type
- if (spectrum.methods.include? :splash_key) || (spectrum.attributes.include? :splash_key)
  td = nah spectrum.splash_key
- else
  td = nah
td = Date.parse(spectrum.created_at).strftime("%Y-%m-%d")
td = view_options