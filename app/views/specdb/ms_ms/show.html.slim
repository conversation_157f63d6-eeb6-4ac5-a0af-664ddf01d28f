.page-header: h1 = title "#{@spectrum.spectrum_type} (#{@spectrum.compound.try(:to_param)})"

.specdb.col-md-10
  .panel.panel-info#details
    .panel-heading Spectrum Details
    .panel-body
      table.spec-table
        tr
          th #{specdb_config :accessor_database_name} ID:
          td =nah get_compound_id(@spectrum.compound)
        tr
          th Compound name:
          td =nah @spectrum.compound.try(:name)
        tr
          th Spectrum type:
          td =nah @spectrum.spectrum_type
        tr
          th Splash Key:
          td
            - if @spectrum.splash_key
              = @spectrum.splash_key
              = " "
              - if !@spectrum.predicted
                = bio_link_out :mona_splash, @spectrum.splash_key, "View in MoNA"
            - else
              = nah
        - if @spectrum.predicted
          tr
            th Notes:
            td = "This is a predicted spectrum and it should only be used as a guide, further evidence is required to confirm identification."
        

  .panel.panel-info#spectrum
    .panel-heading Spectrum View
    .panel-body
      - if @spectrum.spectrum_image
        ul.nav.nav-tabs
          li.active = link_to "Viewer", "#spectrum-viewer", data: { toggle: 'tab' }
          li = link_to "Image", "#spectrum-image", data: { toggle: 'tab' }
        .tab-content
          .tab-pane.active#spectrum-image
            = image_tag(@spectrum.spectrum_image.url, alt: "Image", width: "800")
          .tab-pane#spectrum-viewer
            #spectrum-graph

      - else
        #spectrum-graph


  .panel.panel-info#conditions
    .panel-heading Experimental Conditions
    .panel-body
      - if @spectrum.predicted
        table.spec-table
          tr
            th Ionization Mode:
            td = @spectrum.ionization_mode
          tr
            th Collision Energy:
            td = @spectrum.collision_energy_voltage.to_s + " eV"
          tr
            th Instrument Type:
            td
              ' QTOF (generic), spectrum predicted by 
              = link_to "CFM-ID", "http://cfmid.wishartlab.com"
              '
          tr
            th Mass Resolution:
            td = "0.0001 Da"

          - if !@spectrum.compound.nil?
            tr
              th Molecular Formula:
              td =nah @spectrum.compound.formula
            tr
              th Molecular Weight (Monoisotopic Mass):
              td =nah @spectrum.compound.moldb_mono_mass.round(4).to_s + " Da"
              
          - if has_attr( @spectrum, :base_peak)
            tr
              th Base Peak:
              td =nah @spectrum.base_peak
          - if has_attr( @spectrum, :ri_type)
            tr
              th Retention Index Type:
              td =nah @spectrum.ri_type
          - if has_attr( @spectrum, :retention_index)
            tr
              th Retention Index:
              td =nah @spectrum.retention_index
          - if has_attr( @spectrum, :retention_time)
            tr
              th Retention Time:
              td =nah @spectrum.retention_time
          - if has_attr( @spectrum, :column_type)
            tr
              th Column Type:
              td =nah @spectrum.column_type
          - if has_attr( @spectrum, :derivative_type)
            tr
              th Derivative Type:
              td =nah @spectrum.derivative_type
          - if has_attr( @spectrum, :derivative_formula)
            tr
              th Derivative Formula:
              td =nah @spectrum.derivative_formula
          - if has_attr( @spectrum, :derivative_mw)
            tr
              th Derivative Molecular Weight:
              td =nah @spectrum.derivative_mw

      - elsif @spectrum.conditions_present?
        table.spec-table
          - if has_attr(@spectrum, :sample_concentration)
            tr
              th Sample Concentration:
              td =nah @spectrum.concentration
          - if has_attr( @spectrum, :solvent )
            tr
              th Solvent:
              td =nah @spectrum.solvent
          - if has_attr( @spectrum, :sample_mass )
            tr
              th Sample Mass:
              td =nah @spectrum.mass
          - if has_attr( @spectrum, :sample_assessment )
            tr
              th Sample Assessment:
              td =nah @spectrum.sample_assessment
          - if has_attr( @spectrum, :spectra_assessment)
            tr
              th Spectrum Assessment:
              td =nah @spectrum.spectra_assessment
          - if has_attr(@spectrum, :sample_source)
            tr
              th Sample Source:
              td =nah @spectrum.sample_source
          - if has_attr( @spectrum, :instrument_type )
            tr
              th Instrument Type:
              td =nah @spectrum.instrument_type
          - if has_attr(@spectrum, :mono_mass)
            tr
              th Monoisotopic Mass:
              td =nah @spectrum.mono_mass
          - if has_attr(@spectrum, :energy_field)
            tr
              th Energy Field:
              td =nah @spectrum.energy_field
          - if has_attr(@spectrum, :collision_energy_level)
            tr
              th Collision Energy Level:
              td =nah @spectrum.collision_energy_level
          - if has_attr(@spectrum, :collision_energy_voltage)
            tr
              th Collision Energy Voltage:
              td =nah @spectrum.collision_energy_voltage
          - if has_attr( @spectrum, :ionization_mode )
            tr
              th Ionization Mode:
              td =nah @spectrum.ionization_mode
      - elsif @spectrum.predicted
        ' Spectra predicted by 
        = link_to "CFM-ID", "http://cfmid.wishartlab.com"
        ' .
      - else
        .no-results = nah

  .panel.panel-info#documentation
    .panel-heading Documentation
    .panel-body
      - if @spectrum.respond_to?(:documents) && @spectrum.documents.present?
        table.table.table-condensed.table-striped.unpadded-table
          thead
            tr
              th Document Description
              th Download
          tbody
            - unrestricted_downloads_list(@spectrum).each do |doc|
              tr
                td = doc[0]
                - if doc[1].nil?
                  td = nah
                - else
                  td = link_to "Download file", doc[1], download: ''
                td = nah doc[2]
            tr
              - http = Net::HTTP.new Specdb.config.site.split('//')[1].chomp('/')
              td = "mzML formatted file (MZML)"
              td = link_to "Download file", generate_mzml_ms_ms_path, method: :get
              td = number_to_human_size(@spectrum.to_mz_ml(request).bytesize)

      - else
        .no-results = nah

  .panel.panel-info#references
    .panel-heading References
    .panel-body
      - if @spectrum.references.present? 
        ol
          - @spectrum.references.each do |reference|
            - begin
              - if reference.ref_text.present? # Do not want to display HMDB links
                - if !reference.ref_text.starts_with? 'http' # Do not want to display plain links
                  li
                    | #{reference.ref_text.html_safe}
                    - if reference.pubmed_id.present?
                      | &nbsp;[#{bio_link_out(:pubmed, reference.pubmed_id, "PubMed: #{reference.pubmed_id}")}]
                    - if reference.database.present?
                      - if (reference.database != 'HMDB') && (!reference.database.include? 'MetaboBASE') # MetaboBASE has no link to spectra
                        - if reference.database_id.present?
                          - if reference.ref_text.include? reference.database # Don't want the database link on a separate line if the ref_text refers to it (that's just redundant)
                            | &nbsp;[#{bio_link_out reference.database.downcase.gsub('mona', 'mona_spectrum').gsub('-','_'), reference.database_id}]

              - if reference.database.present? # If no ref link or database is different from ref link
                - if reference.database.include? 'MetaboBASE'
                  li 
                    | Bruker-Sumner MetaboBASE Plant Library
                - elsif (reference.database != 'HMDB') && ((reference.ref_text.blank?) || (!reference.ref_text.include? reference.database))
                  li
                    | #{bio_link_to_source reference.database.downcase.gsub('-','_')}
                    - if reference.database_id.present?
                      | &nbsp;[#{bio_link_out reference.database.downcase.gsub('mona', 'mona_spectrum').gsub('-','_'), reference.database_id}]
            - rescue
              | Error displaying reference
      - else
        .no-results = nah

nav.sidenav.specdb-sidenav.col-md-2
  div data-spy="affix" data-offset-top="90"
    ul.nav.nav-pills.nav-stacked
      li.active = link_to 'Details', '#details'
      li = link_to 'Spectrum', '#spectrum'
      li = link_to 'Experimental Conditions', '#conditions'
      li = link_to 'Documentation', '#documentation'
      li = link_to 'References', '#references'

javascript:
  mz_plot('#spectrum-graph', #{parse_spectra(@spectrum.peaks, false).to_json.html_safe}, ".panel-body");
