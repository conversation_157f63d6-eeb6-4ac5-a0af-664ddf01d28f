.search-spectrum
  p 
    | Your input spectrum is shown on top in 
    span.search-color blue
    | , and the closest matching result spectrum is shown underneath in 
    span.compare-color red
    | . Use the "Compare Spectrum" buttons on the results list below to view and compare different spectra with your input.
  .search-legend
    .legend-item
      .search-color-box
      | Input spectrum
    .legend-item
      .compare-color-box
      | Current comparison result spectrum: 
      span#comparison-compound
  .spectrum-overlay
    = image_tag "loading-line.gif"
  #spectrum-graph

table.table.table-condensed.table-striped.results-table
  thead
    tr
      th Spectral Display Tools
      th Name/CAS Number
      th Weight/Formula
      th Structure
      th Fit(%)
      th RFit(%)
      th Purity(%)
      th CCS
  tbody
    - first = true
    - @results.each do |result|
      - next if result.compound.nil? # Shouldn't happen but alas
      tr
        td style="text-align: left; width: 10%;"
          div class="type #{result.type.downcase}" = result.type
          = link_to "View Full Details", spectrum_path(result), class: "btn-card",
              'data-turbolinks' => false
          - if first
            = link_to "Current Spectrum", "#", class: "compare btn-card btn-current", 'data-spectrum_id' => result.id,
                    disabled: "disabled", 'spec_name' => "ms_ms"
          - else
            = link_to "Compare Spectrum", "#", class: "compare btn-card btn-compare", 'data-spectrum_id' => result.id,
                    'spec_name' => "ms_ms"
        td style="width: 10%;"
          = link_to main_app.url_for([result.compound, {:host => Rails.application.config.url}]) do
            strong
              | #{result.compound.name} (#{get_compound_id(result.compound)})
              hr
              = nah result.compound.cas
        td style="width: 10%;"
          = nah result.compound.average_mass
          hr
          = nah html_formula(result.compound.formula)
        td style="width: 10%;" 
          = moldb_thumbnail(result.compound)
        td style="width: 5%;" 
          = float_or_na result.fit
        td style="width: 5%;" 
          = float_or_na result.r_fit
        td style="width: 5%;"
          = float_or_na result.purity
        td style="width: 5%;"
          - if result.ccs_value.nil?
            = 'N/A'
          - else 
            = float_or_na result.ccs_value
      - if first
        - first = false
        javascript:
          window.compareCandidate = #{ load_compound(result.id, "MsMs").to_json.html_safe };

javascript:
  mz_plot("#spectrum-graph", #{ @search_peaks.to_json.html_safe }, ".search-spectrum", { mirror: true });