.page-header: h1 = title 'Spectra Search <small>Tandom Mass Spectrum</small>'
= render "/specdb/search/navigation", selected: :ms_ms

- if params[:commit].present?
  = form_toggler
.specdb-search-form class=(params[:commit].present? ? 'collapse' : '')
  = form_tag ms_ms_search_path,{ id: 'ms-ms-search-form', class: 'form-horizontal', multipart: true } do
    .form-group
      .col-sm-6
        .form-group
          .col-sm-10
            = label_tag :peaks, 'MS/MS Peak List (m/z & Intensity):', class: 'label-vertical text-area-label'
            = text_area_tag :peaks, params[:peaks], class: 'form-control', rows: '12', placeholder: "Enter one mass (m/z) and intensity corresponding to one peak per line"
            .file-upload-section style="margin-top: 10px;"
              .row
                .col-sm-8
                  = file_field_tag :peaklist_file, class: 'form-control', accept: '.txt', style: 'display: none;', id: 'msms-peaklist-file-input'
                  = button_tag 'Choose File', type: 'button', class: 'btn btn-default btn-sm', id: 'msms-file-upload-btn', onclick: 'document.getElementById("msms-peaklist-file-input").click();'
                  span#msms-file-name-display style="margin-left: 10px; font-style: italic; color: #666;"
                .col-sm-4
                  = button_tag 'Clear', type: 'button', class: 'btn btn-default btn-sm', id: 'msms-clear-file-btn', style: 'display: none;'
              .help-block style="margin-top: 5px; font-size: 12px;"
                | Upload a .txt file with one m/z and intensity value per line as an alternative to manual entry.
      .col-sm-6
        .form-group
          .col-sm-6
            = label_tag :parent_ion_mass, 'Parent Ion Mass (Da):', class: 'label-vertical'
          .col-sm-6
            = text_field_tag :parent_ion_mass, params[:parent_ion_mass], class: 'form-control', placeholder: "e.g. 146"
        .form-group
          .col-sm-6
            = label_tag :parent_ion_mass_tolerance, 'Parent Ion Mass Tolerance ±:', class: 'label-vertical'
          .col-sm-3
            = text_field_tag :parent_ion_mass_tolerance, params[:parent_ion_mass_tolerance], class: 'form-control', placeholder: "e.g. 0.1"
          .col-sm-3
            = select_tag(:parent_ion_mass_tolerance_units, options_for_select(['Da', 'ppm'], params[:parent_ion_mass_tolerance_units]), class: "form-control")
        .form-group
          .col-sm-6
            = label_tag :mass_charge_tolerance, 'Mass/Charge (m/z) Tolerance ±:', class: 'label-vertical'
          .col-sm-3
            = text_field_tag :mass_charge_tolerance, params[:mass_charge_tolerance], class: 'form-control', placeholder: "e.g. 0.5"
          .col-sm-3
            = select_tag(:mass_charge_tolerance_units, options_for_select(['Da', 'ppm'], params[:mass_charge_tolerance_units]), class: "form-control")
        .form-group
          .col-sm-6
            = label_tag :ionization_mode, 'Ionization Mode:', class: 'label-vertical'
          .col-sm-4
            = select_tag :ms_ms_search_ion_mode, options_for_select({Positive: "positive", Negative: "negative"}, params[:ms_ms_search_ion_mode]), {class: "form-control"}
        .form-group
          - selected_adducts = params[:ms_search_ion_mode] == "positive" ? @positive_adducts : @negative_adducts
          .col-sm-6
            = label_tag :collision_energy_level, 'CID Energy:', class: 'label-vertical'
          .col-sm-4
            = select_tag :collision_energy_level, options_for_select([['Low (10V)','low'], ['Medium (25V)','med'], ['High (40V)','high'],['All', nil]], params[:collision_energy_level]), class: 'form-control'
        .form-group
          .col-sm-6
            = label_tag :ccs_value, 'Collision Cross Section (Å²):', class: "label-vertical"
          .col-sm-4
            = text_field_tag :ccs_value, params[:ccs_value], class: 'form-control', placeholder: "e.g. 129.39"
        .form-group  
          .col-sm-6
            = label_tag :ccs_predictors, 'CCS Prediction Method:', class: "label-vertical"
          .col-sm-4
            = select_tag :ccs_predictors, options_for_select(['', 'AllCCS', 'DarkChem', 'DeepCCS'], params[:css_predictors]), { class: "form-control" }
        .form-group
          .col-sm-6
            = label_tag :ccs_tolerance, 'Collision Cross Section Tolerance ± (%):', class: "label-vertical"
          .col-sm-4
            = select_tag :ccs_tolerance, options_for_select([['',nil], ['1', '0.01'], ['3', '0.03'], ['5', '0.05'], ['10', '0.10']], params[:ccs_value]), { class: "form-control" }

        .form-group
          .col-sm-6
          / = label_tag :predicted, 'Include Predicted Spectra:', class: 'col-sm-5'
          .col-sm-6
            / = label_tag :no_predicted, "No", class: "radio-inline" do
            /   = radio_button_tag :predicted, "no", true
            /   ' No
            / = label_tag :yes_predicted, "Yes", class: "radio-inline" do
            /   = radio_button_tag :predicted, "yes", false
            /   ' Yes

            / = check_box_tag :predicted, "1", params[:predicted] == "1" ? true : false
            / = label_tag :predicted, 'Include Predicted Spectra', class: 'check-box-label'

            = label_tag :predicted, raw("#{check_box_tag(:predicted, "1", params[:predicted] == "1" ? true : false)} Include predicted spectra"), class: "check-box-label"

    - if lookup_context.find_all("_filter").any?
      = render "filter"

    hr
    .form-group
      .col-sm-12
        .form-group
          .load-search-buttons
            .col-sm-7
              .col-sm-7
                = link_to "#{glyphicon(:save)} Load Example with CCS".html_safe, "#", class: "btn btn-default example-loader-ms_ms_ccs", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/ms_ms_ccs.json').html_safe + "'"
                = link_to "#{glyphicon(:save)} Load Example without CCS".html_safe, "#", style: "margin-top:5px", class: "btn btn-default example-loader-ms_ms", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/ms_ms.json').html_safe + "'"
              .col-sm-2
                = button_tag "#{glyphicon(:search)} Search".html_safe, name: :commit, class: 'btn btn-primary search-loader', value: 'Search'
          .reset-button
            .col-sm-5
              .col-sm-6
                = button_tag "#{glyphicon(:repeat)} Reset".html_safe, :name => 'reset', :id => 'reset', :type => 'reset', :class => 'btn btn-danger'


- if params[:commit].present?
  hr
  h2#results Search Results
  - if @results.present? && @custom_results_path.present?
    = render @custom_results_path
  - elsif @results.present?
    = render "results"
  - else
    .no-results No Results Found.
