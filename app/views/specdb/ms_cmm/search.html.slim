.page-header: h1 = title 'Spectra Search <small>MS1 - CEU Mass Mediator</small>'
= render "cmm_note"

- if params[:commit].present?
  = form_toggler

.specdb-search-form class=(params[:commit].present? ? 'collapse' : '')
  .form-group
    | (* Mandatory fields)
  = form_tag ms_cmm_search_path, { id: 'ms-cmm-search-form', class: 'form-horizontal' } do
    .form-group
      .col-sm-12
        .form-group
          .col-sm-4
            = label_tag :masses, '<a data-toggle="tooltip" title="statistically significant masses."> * Significant Query Masses (Da) </a>'.html_safe, class: 'label-vertical text-area-label'
            = text_area_tag :masses, params[:masses], class: 'form-control bottom-padding', placeholder: 'Enter one entry per line (maximum of 300 query masses per request)', required: "required", rows: '6'
          .col-sm-4
            = label_tag :retention_times, '<a data-toggle="tooltip" title="RT for statistically significant masses (minutes)."> Significant Retention Times </a>'.html_safe, class: 'label-vertical text-area-label'
            = text_area_tag :retention_times, params[:retention_times], class: 'form-control bottom-padding', placeholder: 'Enter one entry per line', rows: '6'
          .col-sm-4
            = label_tag :composite_spectra, '<a data-toggle="tooltip" title="CS for statistically significant masses. (m/z,intensity) for all clustered ions."> Significant Composite Spectra </a>'.html_safe, class: 'label-vertical text-area-label'
            = text_area_tag :composite_spectra, params[:composite_spectra], class: 'form-control bottom-padding', placeholder: 'load example to see format', rows: '6'
        .form-group
          .col-sm-4
            = label_tag :all_masses, '<a data-toggle="tooltip" title="All (statistically and non-statistically significant) masses."> All Query Masses (Da) </a>'.html_safe, class: 'label-vertical text-area-label'
            = text_area_tag :all_masses, params[:all_masses], class: 'form-control bottom-padding', placeholder: 'Enter one entry per line', rows: '6'
          .col-sm-4
            = label_tag :all_retention_times, '<a data-toggle="tooltip" title="RT for all masses (minutes)."> All Retention Times </a>'.html_safe, class: 'label-vertical text-area-label'
            = text_area_tag :all_retention_times, params[:all_retention_times], class: 'form-control bottom-padding', placeholder: 'Enter one entry per line', rows: '6'
          .col-sm-4
            = label_tag :all_composite_spectra, '<a data-toggle="tooltip" title="CS for all masses. (m/z,intensity) for all clustered ions."> All Composite Spectra </a>'.html_safe, class: 'label-vertical text-area-label'
            = text_area_tag :all_composite_spectra, params[:all_composite_spectra], class: 'form-control bottom-padding', placeholder: 'load example to see format', rows: '6'
      .col-sm-6
        .form-group
          .col-sm-6
            = label_tag :ms_search_ion_mode, "* Ion Mode:", class: "label-vertical"
          .col-sm-4
            = select_tag :ms_search_ion_mode, options_for_select({ 'Positive' => "positive", 'Negative' => "negative", 'Neutral' => 'neutral'}, params[:ms_search_ion_mode]), { class: "form-control" }
        .form-group
          - adducts = selected_adducts(params[:adduct_type], params[:ms_search_ion_mode])
          .col-sm-6
            = label_tag :adduct_type, "* Adduct Type:", class: "label-vertical"
          .col-sm-5
            = select_tag :adduct_type, adducts, {multiple: true, class: "form-control ion-select",
                   'data-positive-adducts' => @positive_adducts.to_json,
                   'data-negative-adducts' => @negative_adducts.to_json,
                   'data-neutral-adducts' => @neutral_adducts.to_json,
                   'data-remember-selected' => params[:adduct_type]}
            .help-block
              ' Hold Ctrl (
              = image_tag 'specdb/windows_logo.svg', class: 'windows-logo'
              '  ) or Command (
              = image_tag 'specdb/apple_logo.svg', class: 'mac-logo'
              '  ) to select multiple adducts.
      .col-sm-6
        .form-group
          .col-sm-6
            = label_tag :tolerance, '* Molecular Weight Tolerance ±:', class: "label-vertical"
          .col-sm-3
            = text_field_tag :tolerance, params[:tolerance], class: 'form-control', placeholder: "e.g. 0.05"
          .col-sm-3
            = select_tag :tolerance_units, options_for_select(['mDa', 'ppm'], params[:tolerance_units]), { class: "form-control" }
        .form-group
          .col-sm-6
            = label_tag :chemical_alphabet, '<a data-toggle="tooltip" title="Search restricted to selected elements."> * Chemical Alphabet </a>'.html_safe, class: 'label-vertical'
          .col-sm-4
            = select_tag :chemical_alphabet, options_for_select({ 'All' => "all", 'CHNOPS' => "chnops", 'CHNOPS + Cl' => "chnopscl"}, params[:chemical_alphabet]), { class: 'form-control bottom-padding' }
        .form-group
          .col-sm-6
            = label_tag :modifiers_type, '<a data-toggle="tooltip" title="Modifiers added to the mobile phase."> * Modifiers </a>'.html_safe, class: 'label-vertical'
          .col-sm-4
            = select_tag :modifiers_type, options_for_select({ 'None' => "none", 'NH3' => "nh3", 'HCOO' => "hcoo", 'HCOO' => "hcoo", 'CH3COO' => "ch3coo", 'HCOONH3' => "hcoonh3", 'CH3COONH3' => "ch3coonh3"}, params[:modifiers_type]), { class: 'form-control bottom-padding' }
        .form-group
          .col-sm-6
            = label_tag :metabolites_type, '<a data-toggle="tooltip" title="Restrict search to the specific compounds type."> * Metabolites type </a>'.html_safe, class: 'label-vertical'
          .col-sm-4
            = select_tag :metabolites_type, options_for_select({ 'All except peptides' => "all-except-peptides", 'Only lipids' => "only-lipids", 'All including peptides' => "all-including-peptides"}, params[:metabolites_type]), { class: 'form-control bottom-padding' }
        .form-group
          .col-sm-6
          .col-sm-6
            = label_tag :deuterium, raw("#{check_box_tag(:deuterium, "1", params[:deuterium] == "1" ? true : false)} Deuterium"), class: "check-box-label"

    - if lookup_context.find_all("/specdb/search/ms/_filter").any?
      = render "/specdb/search/ms/filter"

    hr
    .form-group
      .col-sm-12
        .form-group
          .load-search-buttons
            .col-sm-6
              .col-sm-6
                = link_to "#{glyphicon(:save)} Load Example".html_safe, "#", class: "btn btn-default example-loader", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/ms_cmm.json').html_safe + "'"
              .col-sm-2
                = button_tag "#{glyphicon(:search)} Search".html_safe, name: :commit, class: "btn btn-primary search-loader", value: 'Search'
          .reset-button
            .col-sm-6
              .col-sm-6
                = button_tag "#{glyphicon(:repeat)} Reset".html_safe, :name => 'reset', :id => 'reset', :type => 'reset', :class => 'btn btn-danger'

- if params[:commit].present?
  / hr
  / - if @searches.present?
    #ms-export-csv.pull-right
      = form_tag "http://specdb.wishartlab.com/ms_cmm/search.csv",
          class: "button_to" do |f|
        - @search_params.each do |key,value|
          = hidden_field_tag key, value
        = hidden_field_tag "database", specdb_config(:accessor_database_name)
        = submit_tag "Download Results As CSV", class: 'btn btn-primary btn-xs'
  h2#results Search Results
  - if @searches.present?
    = render "results"
  - else
    .no-results Enter at least one query mass to get a result.
