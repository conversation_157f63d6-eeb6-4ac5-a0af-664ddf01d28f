- if not @searches.empty?
  - @searches.each do |key, values|
    .panel.panel-default.ms-search-result
      .panel-heading
        strong #{glyphicon('info-sign')} Metabolites found for m/z: #{'%.3f'%key[1]} and retention time: #{'%.3f'%key[0]}
      .panel-body
        .scrollWrapper
          table.table.table-condensed class="ms_cmm_table"
            thead
              tr        
                th Compound
                th Name
                th Formula
                th Molecular Weight
                th Adduct
                th <a data-toggle="tooltip" title="Score based on adduct formation rules."> Ionization Score </a>
                th <a data-toggle="tooltip" title="Score based on Adducts relations between different features."> Relation Score </a>
                th <a data-toggle="tooltip" title="Score based on Retention Time depending on double bonds and chain length."> RT score </a>
                th <a data-toggle="tooltip" title="Final score based on previous rules."> Final Score </a>
                th Cas
                th Pubchem
                th Metlin
                th LipidMaps
                th KEGG
            tbody
              - values.each do |value|
                - next if value.nil?
                tr       
                  td = link_to value["hmdb_compound"], value["hmdb_uri"], target: "_blank"
                  td.truncate= value["name"]
                  td = value["formula"]
                  td = value["molecular_weight"]
                  td = value["adduct"]
                  td = value["ionizationScore"].to_i < 0 ? nah(nil, "N/A") : value["ionizationScore"]
                  td = value["adductRelationScore"].to_i < 0 ? nah(nil, "N/A") : value["adductRelationScore"]
                  td = value["RTscore"].to_i < 0 ? nah(nil, "N/A") : value["RTscore"]
                  td = value["finalScore"].to_i < 0 ? nah(nil, "N/A") : value["finalScore"]
                  td = nah value["cas"], "N/A"
                  td = value["pubchem_compound"].present? ? (link_to value["pubchem_compound"], value["pubchem_uri"], target: "_blank") : nah(nil, "N/A")
                  td = value["metlin_compound"].present? ? (link_to value["metlin_compound"], value["metlin_uri"], target: "_blank") : nah(nil, "N/A")
                  td = value["lipidmaps_compound"].present? ? (link_to value["lipidmaps_compound"], value["lipidmaps_uri"], target: "_blank") : nah(nil, "N/A")
                  td = value["kegg_compound"].present? ? (link_to value["kegg_compound"], value["kegg_uri"], target: "_blank") : nah(nil, "N/A")

- else
  .alert.alert-warning No results for this query mass
