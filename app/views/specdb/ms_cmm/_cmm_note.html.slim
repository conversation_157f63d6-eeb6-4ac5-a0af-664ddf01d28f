- notice = '<p>The CMM search is an external service provided and supported by CEMBIO (Center of Metabolomics and bioanalysis of University San Pablo-CEU, Spain). It calculates a score over the annotations based on:<p class="tab">1. Probability of the compound to form a specific adduct.</p><p class="tab">2. Presence of other adducts coming from the same signal in the same RT window.</p><p class="tab">3. RT of the lipids with the same head depending on the number of carbons and double bonds (and, consequently, the hydrophobicity of them).</p></p><p>It allows the user to filter the list of results based on the chemical alphabet, the presence on one or more specific databases and the metabolites type. It uses the experimental settings (modifier) for the ionization rules (1). It takes the information from the mass spectrometer (m/z) for the search into the databases. It uses the RT for the application of adduct relation rules (2) and the hydrophobicity of lipids pertaining to the same class depending on the number of carbons and double bonds (3). It takes advantage of the pre-processing software tools to group the peaks arising from the same primal signal to detect the adduct formed. For a more detailed information about the scores over the annotations, we suggest to visit the official manual (http://ceumass.eps.uspceu.es/manuals.xhtml).<br>CMM service is available from HMDB, but all the issues and suggestions regarding this search should be addressed to the CEMBIO team (<EMAIL>).<br>If you use this service, please also cite this <a href="http://ceumass.eps.uspceu.es/index.xhtml" target="_blank">source</a>.</p>'.html_safe

- flash.now[:notice] = notice
