= db_paginate @results

table.table.table-condensed.table-striped
  thead
    tr
      th Compound
      th Name
      th
        | Molecular Weight /
        br
        |  Chemical Formula
      th Structure
      th Score
      th Match Ratio
  tbody
    - @results.each do |result|
      - next if result.compound.nil?
      tr
        td
          = link_to "#{get_compound_id(result.compound)}", url_for([main_app, result.compound]), class: "btn-card",
                  'data-turbolinks' => false
        / TODO: Implement mz-plots
        / br
        / = link_to "Compare Spectrum", "#", class: "compare btn-card btn-compare",
        /         'data-spectrum_id' => result.id, 'spec_name' => "nmr_one_d"
        td
          = link_to url_for([main_app,result.compound]) do
            strong
              | #{result.compound_name}
        td
          = nah result.compound.try(:mono_mass)
          hr
          = nah html_formula(result.compound.try(:moldb_formula))

        td
          = moldb_thumbnail(result.any_database_id || "missing")
        - if @search.search_type == 'pure_compound'
          td
            = result.dot_score
        - else
          td
            = result.jaccard_index
        td
          = result.match_ratio

= db_paginate @results
