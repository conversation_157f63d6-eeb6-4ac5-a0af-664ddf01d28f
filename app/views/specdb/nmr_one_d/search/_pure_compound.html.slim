.form-group
  .col-sm-12
    .row
      .col-sm-5
        = label_tag :peaks, 'Chemical Peaks', class: 'label-vertical text-area-label'
        = text_area_tag :peaks, params[:peaks], class: 'form-control', style: 'resize: none;',
                rows: '20', placeholder: "Enter one peak position per line"
        .file-upload-section style="margin-top: 10px;"
          .row
            .col-sm-8
              = file_field_tag :peaks_file, class: 'form-control', accept: '.txt', style: 'display: none;', id: 'nmr-peaks-file-input'
              = button_tag 'Choose Peaks File', type: 'button', class: 'btn btn-default btn-sm', id: 'nmr-peaks-file-upload-btn', onclick: 'document.getElementById("nmr-peaks-file-input").click();'
              span#nmr-peaks-file-name-display style="margin-left: 10px; font-style: italic; color: #666; font-size: 11px;"
            .col-sm-4
              = button_tag 'Clear', type: 'button', class: 'btn btn-default btn-sm', id: 'nmr-peaks-clear-file-btn', style: 'display: none;'
          .help-block style="margin-top: 5px; font-size: 11px;"
            | Upload a .txt file with one peak position per line.
      .col-sm-5
        = label_tag :intensities, 'Intensities', class: 'label-vertical text-area-label'
        = text_area_tag :intensities, params[:intensities], class: 'form-control', style: 'resize: none;',
                rows: '20', placeholder: "Enter one peak intensity per line respective of its chemical shift"
        .file-upload-section style="margin-top: 10px;"
          .row
            .col-sm-8
              = file_field_tag :intensities_file, class: 'form-control', accept: '.txt', style: 'display: none;', id: 'nmr-intensities-file-input'
              = button_tag 'Choose Intensities File', type: 'button', class: 'btn btn-default btn-sm', id: 'nmr-intensities-file-upload-btn', onclick: 'document.getElementById("nmr-intensities-file-input").click();'
              span#nmr-intensities-file-name-display style="margin-left: 10px; font-style: italic; color: #666; font-size: 11px;"
            .col-sm-4
              = button_tag 'Clear', type: 'button', class: 'btn btn-default btn-sm', id: 'nmr-intensities-clear-file-btn', style: 'display: none;'
          .help-block style="margin-top: 5px; font-size: 11px;"
            | Upload a .txt file with one intensity value per line.
      #chemical-shift-error.invalid-feedback