.page-header: h1 = title 'Spectra Search <small>NMR Spectrum</small>'
= render "/specdb/search/navigation", selected: :nmr_1d

.specdb-search-form
  = form_tag nmr_one_d_search_index_path, class: 'form-horizontal', multipart: true, onsubmit: 'return validateNmrSearch()' do
    .form-group
      .col-sm-6
        .well-content.form-wrapper id="search_html" style="background-color: #F5F5F5;"
      .col-sm-6#nmr-right-form
        .form-group
          .col-sm-6
            = label_tag :search_type, 'Search Type:', class: 'label-vertical'
          .col-sm-6.nmr-search-input-col
            = select_tag :search_type, options_for_select([%w[Pure\ Compound pure_compound], %w[Mixture mixture]]),
                    class: 'form-control'
            i [title=search_type_text
              data-toggle="tooltip" data-placement="right auto" data-html="true"] = glyphicon('question-sign')
        .form-group
          .col-sm-6
            = label_tag :nucleus, 'Spectra Library:', class: 'label-vertical'
          .col-sm-6.nmr-search-input-col
            = select_tag :nucleus, options_for_select([['1H NMR','1H'], ['13C NMR','13C']], params[:nucleus]),
                    class: 'form-control'
            i [title=spectra_library_text
              data-toggle="tooltip" data-placement="right auto" data-html="true"] = glyphicon('question-sign')
        = hidden_field_tag :database, Specdb.config.accessor_database_name
        .form-group
          .col-sm-6
            a.advanced[aria-controls="advanced-collapse" aria-expanded="false"
            data-toggle="collapse" href="#advanced-collapse" role="button" class="float-right"]
              | Advance Search Options&nbsp
              i = glyphicon('triangle-bottom')
          .col-sm-6
        = render 'advanced_search'
    hr
    .form-group
      .col-sm-6
        .nmr-search-buttons
          = button_tag "#{glyphicon(:repeat)} Reset".html_safe, :name => 'reset', :id => 'reset',
                  :type => 'reset', :class => 'btn btn-danger'
          = link_to "#{glyphicon(:save)} Load Example".html_safe, "#", class: "btn btn-default example-loader-nmr-search",
                  'data-turbolinks': false,
                  'data-example': "'" + (render '/specdb/search/examples/nmr_one_d.json').html_safe + "'"
      .col-sm-6 style="text-align: right;"
        = button_tag "#{glyphicon(:search)} Search".html_safe, name: :commit,
                class: 'btn btn-primary search-loader', value: 'Search',
                id: "nmr-search-button"
  = render partial: 'search_logic'
