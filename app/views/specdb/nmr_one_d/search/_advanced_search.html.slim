#advanced-collapse.collapse
  .form-group
    .col-sm-6
      = label_tag :cs_tolerance, 'Tolerance ± (ppm):', class: 'label-vertical'
    .col-sm-6.nmr-search-input-col
      = text_field_tag :cs_tolerance, params[:cs_tolerance], class: 'form-control', placeholder: 'e.g. 0.2'
      i [title=tolerance_text
        data-toggle="tooltip" data-placement="right auto" data-html="true"] = glyphicon('question-sign')
  .form-group#frequency-form-group
    .col-sm-6
      = label_tag :frequency, class: 'label-vertical dummy-class', style: "margin-right: 5px;" do
        |Frequency (MHz):
    .col-sm-6.nmr-search-input-col
      = text_field_tag :frequency, params[:frequency], class: 'form-control', placeholder: 'e.g. 500', id: "nmr-search-freq"
      i [title=frequency_text
        data-toggle="tooltip" data-placement="right auto" data-boundary="window" data-html="true"] = glyphicon('question-sign')
  .form-group
    .col-sm-12
      p#error-ctn.invisible
        | Error Message Holder
