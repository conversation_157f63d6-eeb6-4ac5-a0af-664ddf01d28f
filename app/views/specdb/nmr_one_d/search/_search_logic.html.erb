<script>
    $(document).ready(function() {
        console.log('=== NMR Search Logic Debug ===');
        console.log('jQuery version:', $.fn.jquery);
        console.log('Document ready fired');

        // Check if search_html element exists
        var searchHtml = $('#search_html');
        console.log('search_html element found:', searchHtml.length > 0);
        console.log('search_html element:', searchHtml);

        if(searchHtml.length) {
            console.log('search_html element exists, proceeding...');

            let pure_compound = "<%= j (render partial: 'pure_compound') %>";
            let mixture = "<%= j (render partial: 'mixture') %>";

            console.log('pure_compound template length:', pure_compound.length);
            console.log('mixture template length:', mixture.length);
            console.log('pure_compound template preview:', pure_compound.substring(0, 100) + '...');
            console.log('mixture template preview:', mixture.substring(0, 100) + '...');

            // Check if search_type element exists
            var searchType = $('#search_type');
            console.log('search_type element found:', searchType.length > 0);
            console.log('search_type element:', searchType);
            console.log('search_type current value:', searchType.val());

            if ($('#search_type').val() === 'pure_compound') {
                console.log('Setting initial content to pure_compound');
                try {
                    $('#search_html').html(pure_compound);
                    console.log('Content set successfully, search_html now contains:', $('#search_html').html().substring(0, 100) + '...');
                } catch (e) {
                    console.error('Error setting pure_compound content:', e);
                }
            } else if ($('#search_type').val() === 'mixture') {
                console.log('Setting initial content to mixture');
                try {
                    $('#search_html').html(mixture);
                    console.log('Content set successfully, search_html now contains:', $('#search_html').html().substring(0, 100) + '...');
                } catch (e) {
                    console.error('Error setting mixture content:', e);
                }
            } else {
                console.log('ERROR: Unexpected search_type value:', $('#search_type').val());
                console.log('search_type element details:', {
                    exists: $('#search_type').length > 0,
                    value: $('#search_type').val(),
                    options: $('#search_type option').map(function() { return $(this).val(); }).get()
                });
                alert('NMR Search Failed. Please inform the <%= specdb_config :accessor_database_name %> team with instructions on how you reached this page.');
                throw('NMR Search Failed. Please inform the <%= specdb_config :accessor_database_name %> Team with instructions on how you reached this page.');
            }

            $('#search_type').on('change', function () {
                console.log('=== Search Type Changed ===');
                console.log('New value:', this.value);

                if (this.value === "pure_compound") {
                    console.log('Changing to pure_compound');
                    try {
                        $('#search_html').html(pure_compound);
                        console.log('Content changed successfully, search_html now contains:', $('#search_html').html().substring(0, 100) + '...');
                    } catch (e) {
                        console.error('Error changing to pure_compound:', e);
                    }
                } else if (this.value === "mixture") {
                    console.log('Changing to mixture');
                    try {
                        $('#search_html').html(mixture);
                        console.log('Content changed successfully, search_html now contains:', $('#search_html').html().substring(0, 100) + '...');
                    } catch (e) {
                        console.error('Error changing to mixture:', e);
                    }
                } else {
                    console.log('ERROR: Unexpected search_type change value:', this.value);
                    alert('NMR Search Failed. Please inform the <%= specdb_config :accessor_database_name %> Team with instructions on how you reached this page.');
                    throw('NMR Search Failed. Please inform the <%= specdb_config :accessor_database_name %> Team with instructions on how you reached this page.');
                }
            });

            console.log('Change event handler attached to search_type');
        } else {
            console.log('ERROR: search_html element not found!');
            console.log('Available elements with id containing "search":', $('[id*="search"]').map(function() { return this.id; }).get());
        }

        console.log('=== End NMR Search Logic Debug ===');
    });
</script>