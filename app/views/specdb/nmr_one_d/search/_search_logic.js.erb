$(document).ready(function() {
    if($('#search_html').length) {
        let pure_compound = "<%= j (render partial: 'pure_compound') %>";
        let mixture = "<%= j (render partial: 'mixture') %>";

        if ($('#search_type').val() === 'pure_compound') {
            $('#search_html').html(pure_compound);
        } else if ($('#search_type').val() === 'mixture') {
            $('#search_html').html(mixture);
        } else {
            console.log($('#search_type').val())
            alert('NMR Search Failed. Please inform the <%= specdb_config :accessor_database_name %> team.');
            throw('NMR Search Failed.');
        }

        $('#search_type').on('change', function () {
            if (this.value === "pure_compound") {
                $('#search_html').html(pure_compound);
            } else if (this.value === "mixture") {
                $('#search_html').html(mixture);
            } else {
                console.log(this.value)
                alert('NMR Search Failed. Please inform the <%= specdb_config :accessor_database_name %> team.');
                throw('NMR Search Failed.');
            }
        });
    }
});