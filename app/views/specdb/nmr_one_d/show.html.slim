- if lookup_context.find_all("/specdbi/nmr_one_d/_show.html.slim").any?
  = render '/specdbi/nmr_one_d/show.html.slim', spectrum: @spectrum
- else
  .page-header: h1 = title "#{@spectrum.spectrum_type} (#{@spectrum.compound.try(:to_param)})"

  .specdb.col-md-10
    .panel.panel-info#details
      .panel-heading Spectrum Details
      .panel-body
        table.spec-table
          tr
            th #{specdb_config :accessor_database_name} ID:
            td =nah get_compound_id(@spectrum.compound)
          tr
            th Compound name:
            td =nah @spectrum.compound.try(:name)
          tr
            th Spectrum type:
            td =nah @spectrum.spectrum_type

    .panel.panel-info#spectrum
      .panel-heading Spectrum View
      .panel-body
        = render 'spectra_viewer'

    .panel.panel-info#conditions
      .panel-heading Experimental Conditions
      .panel-body
        - if @spectrum.conditions_present?
          table.spec-table
            - if has_attr(@spectrum, :sample_concentration)
              tr
                th Sample Concentration:
                td =nah @spectrum.concentration.to_f.round(3).to_s + " " + @spectrum.sample_concentration_units
            - if has_attr( @spectrum, :solvent )
              tr
                th Solvent:
                td =nah @spectrum.solvent.gsub(/(\d+)/, '<sub>\0</sub>')
            - if has_attr( @spectrum, :sample_mass )
              tr
                th Sample Mass:
                td =nah @spectrum.mass
            - if has_attr( @spectrum, :sample_assessment )
              tr
                th Sample Assessment:
                td =nah @spectrum.sample_assessment
            - if has_attr( @spectrum, :spectra_assessment)
              tr
                th Spectrum Assessment:
                td =nah @spectrum.spectra_assessment
            - if has_attr(@spectrum, :sample_source)
              tr
                th Sample Source:
                td =nah @spectrum.sample_source
            - if has_attr( @spectrum, :instrument_type )
              tr
                th Instrument Type:
                td =nah @spectrum.instrument_type
            - if has_attr(@spectrum, :nucleus)
              tr
                th Nucleus:
                td =nah @spectrum.nucleus.gsub(/(\d+)/, '<sup>\0</sup>')
            - if has_attr( @spectrum, :frequency )
              - if @spectrum.frequency =~ /MHz/
                  tr
                    th Frequency:
                    td =nah @spectrum.frequency
              - else
                  tr
                    th Frequency:
                    td =nah @spectrum.frequency + " MHz"
            - if has_attr(@spectrum, :sample_ph)
              tr
                th Sample pH:
                td =nah @spectrum.sample_ph
            - if has_attr(@spectrum, :sample_temperature)
              tr
                th Sample Temperature:
                td =nah @spectrum.temperature
            - if has_attr( @spectrum, :chemical_shift_reference )
              tr
                th Chemical Shift Reference:
                td =nah @spectrum.chemical_shift_reference
        - else
          .no-results = nah

    .panel.panel-info#documentation
      .panel-heading Documentation
      .panel-body
        - if @spectrum.documents.present?
          table.table.table-condensed.table-striped.unpadded-table
            thead
              tr
                th Document Description
                th Download
                th File Size
            tbody
              - downloads_list(@spectrum).each do |doc|
                tr
                  td = doc[0]
                  - if doc[1].nil?
                    td = nah
                  - else
                    td = link_to "Download file",
                            (doc[1].url.include?('https') ? doc[1].url : doc[1].url.gsub('http', 'https')),
                            download: ''
                  td = nah doc[2]

        - else
          .no-results = nah

    .panel.panel-info#references
      .panel-heading References
      .panel-body
        - if @spectrum.references.present?
          ol
            - @spectrum.references.each do |reference|
              - begin
                - if reference.ref_text.present? # Do not want to display HMDB links
                  - if !reference.ref_text.starts_with? 'http' # Do not want to display plain links
                    li
                      | #{reference.ref_text.html_safe}
                      - if reference.pubmed_id.present?
                        | &nbsp;[#{bio_link_out(:pubmed, reference.pubmed_id, "PubMed: #{reference.pubmed_id}")}]
                      - if reference.database.present?
                        - if (reference.database != 'HMDB') && (!reference.database.include? 'MetaboBASE') # MetaboBASE has no link to spectra
                          - if reference.database_id.present?
                            - if reference.ref_text.include? reference.database # Don't want the database link on a separate line if the ref_text refers to it (that's just redundant)
                              | &nbsp;[#{bio_link_out reference.database.downcase.gsub('mona', 'mona_spectrum').gsub('-','_'), reference.database_id}]

                - if reference.database.present? # If no ref link or database is different from ref link
                  - if reference.database.include? 'MetaboBASE'
                    li
                      | Bruker-Sumner MetaboBASE Plant Library
                  - elsif (reference.database != 'HMDB') && ((reference.ref_text.blank?) || (!reference.ref_text.include? reference.database))
                    li
                      | #{bio_link_to_source reference.database.downcase.gsub('-','_')}
                      - if reference.database_id.present?
                        | &nbsp;[#{bio_link_out reference.database.downcase.gsub('mona', 'mona_spectrum').gsub('-','_'), reference.database_id}]
              - rescue
                | Error displaying reference
        - else
          .no-results = nah

  nav.sidenav.specdb-sidenav.col-md-2
    div data-spy="affix" data-offset-top="90"
      ul.nav.nav-pills.nav-stacked
        li.active = link_to 'Details', '#details'
        li = link_to 'Spectrum', '#spectrum'
        li = link_to 'Experimental Conditions', '#conditions'
        li = link_to 'Documentation', '#documentation'
        li = link_to 'References', '#references'
