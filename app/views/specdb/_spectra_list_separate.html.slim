- spectra = compound.spectra
- if @no_spectra_connection
  .alert.alert-danger Sorry, spectra are temporarily unavailable.
- elsif spectra.blank?
  = nah
- else
  - sorted_spectra = sorted_spectra(spectra)
  tr
    th EI-MS/GC-MS
    - if sorted_spectra[:gcms].present? or sorted_spectra[:predicted_gcms].present? or sorted_spectra[:eims].present?
      td.data-table-container
        table.table-inner
          thead
            tr
              th.spectrum-type Type
              th.description Description
              th.splash-key = link_to "Splash Key", "http://splash.fiehnlab.ucdavis.edu/", target: "_blank"
              th.view-link View
          tbody
            - sorted_spectra[:eims].each do |spectrum|
              tr
                td EI-MS
                = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'eims'
            - sorted_spectra[:gcms].each do |spectrum|
              tr
                td GC-MS
                = render '/specdb/spectra_list_data_c_ms', spectrum: spectrum, type: 'gcms'

            - sorted_spectra[:predicted_gcms].each do |spectrum|
              tr
                td Predicted GC-MS
                = render '/specdb/spectra_list_data_c_ms', spectrum: spectrum, type: 'predicted-gcms'
    - else
      td = nah

  tr
    th MS/MS
    - if sorted_spectra[:msms].present? or sorted_spectra[:predicted_msms].present?
      td.data-table-container
        table.table-inner
          thead
            tr
              th.spectrum-type Type
              th.description Description
              th.splash-key = link_to "Splash Key", "http://splash.fiehnlab.ucdavis.edu/", target: "_blank"
              th.view-link View
          tbody
            - sorted_spectra[:msms].each do |spectrum|
              tr
                td MS/MS
                = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'msms'

            - sorted_spectra[:predicted_msms].each do |spectrum|
              tr
                td Predicted MS/MS
                = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'predicted-msms'
    - else
      td = nah

  tr
    th NMR
    - if sorted_spectra[:eims].present? or sorted_spectra[:nmr2d].present?
      td.data-table-container
        table.table-inner
          thead
            tr
              th.spectrum-type Type
              th.description Description
              th
              th.view-link View
          tbody
            - sorted_spectra[:nmr1d].each do |spectrum|
              tr
                td 1D NMR
                = render '/specdb/spectra_list_data_nmr', spectrum: spectrum
            - sorted_spectra[:nmr2d].each do |spectrum|
              tr
                td 2D NMR
                = render '/specdb/spectra_list_data_nmr', spectrum: spectrum
    - else
      td = nah
