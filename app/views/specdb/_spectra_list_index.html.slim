- spectra = compound.spectra
- if @no_spectra_connection
  .alert.alert-danger Sorry, spectra are temporarily unavailable.
- elsif spectra.blank?
  = nah
- else
  - sorted_spectra = sorted_spectra(spectra)
  table.table-inner
    thead
    tbody
      / NMR spectra do not have splash keys
      - if sorted_spectra[:nmr1d].present?
        - sorted_spectra[:nmr1d].each do |spectrum|
          tr
            = render '/specdb/spectra_list_data_index', spectrum: spectrum, type: 'nmr'

      - if sorted_spectra[:nmr2d].present?
        - sorted_spectra[:nmr2d].each do |spectrum|
          tr
            = render '/specdb/spectra_list_data_index', spectrum: spectrum, type: 'nmr'
