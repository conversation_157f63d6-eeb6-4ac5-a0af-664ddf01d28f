- spectra = compound.spectra
- if @no_spectra_connection
  .alert.alert-danger Sorry, spectra are temporarily unavailable.
- elsif spectra.blank?
  = nah
- else
  - sorted_spectra = sorted_spectra(spectra)
  table.table-inner
    thead
      tr
        th.head-medium Spectrum Type
        th.head-medium Description
        th.head-medium = link_to "Splash Key", "http://splash.fiehnlab.ucdavis.edu/", target: "_blank"
        th.head-medium Deposition Date
        th View
    tbody
      - if sorted_spectra[:gcms].present?
        - sorted_spectra[:gcms].each do |spectrum|
          tr
            td GC-MS
            = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'gcms'

      - if sorted_spectra[:predicted_gcms].present?
        - sorted_spectra[:predicted_gcms].each do |spectrum|
          tr
            td Predicted GC-MS
            = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'predicted-gcms'

      - if sorted_spectra[:msms].present?
        - sorted_spectra[:msms].each do |spectrum|
          tr
            td LC-MS/MS
            = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'msms'

      - if sorted_spectra[:predicted_msms].present?
        - sorted_spectra[:predicted_msms].each do |spectrum|
          tr
            td Predicted LC-MS/MS
            = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'predicted-msms'

      - if sorted_spectra[:eims].present?
        - sorted_spectra[:eims].each do |spectrum|
          tr
            td MS
            = render '/specdb/spectra_list_data', spectrum: spectrum, type: 'eims'

      / NMR spectra do not have splash keys
      - if sorted_spectra[:nmr1d].present?
        - sorted_spectra[:nmr1d].each do |spectrum|
          tr
            td 1D NMR
            = render '/specdb/spectra_list_data_nmr', spectrum: spectrum, type: 'nmr'

      - if sorted_spectra[:nmr2d].present?
        - sorted_spectra[:nmr2d].each do |spectrum|
          tr
            td 2D NMR
            = render '/specdb/spectra_list_data_nmr', spectrum: spectrum, type: 'nmr'
