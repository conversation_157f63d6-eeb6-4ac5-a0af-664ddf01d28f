.modal-header
  button.close aria-hidden="true" data-dismiss="modal" type="button"  &times;
  h4.modal-title ESI Adducts of "#{@compound_name}": #{@formula}
  <br>       
  h Neutral M = #{convert_to_signf(@ms_search_compound.moldb_mono_mass)} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;                               
.modal-body
.modal-body
  dl.dl-horizontal
    - if @mz_cal_values.present?
      table.calculator-table
        thead
          th ADDUCT
          th CHARGE
          th M/Z

        tbody
          - @mz_cal_values.each do |h|
            - if h.present?
              tr
                td = h[:adduct]
                td = h[:charge]
                td = convert_to_signf(h[:mz])

              
    - else
      .alert.alert-warning No results for this compound

.modal-footer
  button.btn.btn-default data-dismiss="modal" type="button" Close



                