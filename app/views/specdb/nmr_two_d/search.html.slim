.page-header: h1 = title 'Spectra Search <small>2D NMR Spectrum</small>'
- if params[:nmr].present? && params[:nmr] == 'true'
  = render "/specdb/search/nmr_navigation", selected: :nmr_2d
- else
  = render "/specdb/search/navigation", selected: :nmr_2d

- if params[:commit].present?
  = form_toggler

.specdb-search-form class=(params[:commit].present? ? 'collapse' : '')
  = form_tag nmr_two_d_search_path, { class: 'form-horizontal' } do
    .form-group
      .col-sm-6
        .form-group
          .col-sm-10
            = label_tag :peaks, 'Cross-Peak Chemical Shift List:', class: 'label-vertical text-area-label'
            = text_area_tag :peaks, params[:peaks], class: 'form-control', rows: '10', placeholder: "Enter one cross-peak chemical shift per line with the numbers seperated by a space"
      .col-sm-6
        .form-group
          .col-sm-6
            = label_tag :library, 'Spectra Library:', class: 'label-vertical'
          .col-sm-4
            = select_tag :library, options_for_select([['2D TOCSY','tocsy'],['13C HSQC','hsqc']], params[:library]), class: 'form-control'       
        .form-group
          .col-sm-6
            = label_tag :x_tolerance, 'X-axis Tolerance ± (ppm):', class: 'label-vertical'
          .col-sm-4
            = text_field_tag :x_tolerance, params[:x_tolerance], class: 'form-control', placeholder: "e.g. 0.02"
        .form-group
          .col-sm-6
            = label_tag :y_tolerance, 'Y-axis Tolerance ± (ppm):', class: 'label-vertical'
          .col-sm-4
            = text_field_tag :y_tolerance, params[:y_tolerance], class: 'form-control', placeholder: "e.g. 0.02"

    - if lookup_context.find_all("/specdb/search/nmr_two_d/_filter").any?
      = render "/specdb/search/nmr_two_d/filter"

    hr
    .form-group
      .col-sm-12
        .form-group
          .load-search-buttons
            .col-sm-6
              .col-sm-6
                = link_to "#{glyphicon(:save)} Load Example".html_safe, "#", class: "btn btn-default example-loader", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/nmr_two_d.json').html_safe + "'"
              .col-sm-2
                = button_tag "#{glyphicon(:search)} Search".html_safe, name: :commit, class: 'btn btn-primary search-loader', value: 'Search'
          .reset-button
            .col-sm-6
              .col-sm-6
                = button_tag "#{glyphicon(:repeat)} Reset".html_safe, :name => 'reset', :id => 'reset', :type => 'reset', :class => 'btn btn-danger'

- if params[:commit].present?
  hr
  h2#results Search Results
  - if @results.present? && @custom_results_path.present?
    = render @custom_results_path
  - elsif @results.present?
    = render "results"
  - else
    .no-results No Results Found.
