table.table.table-condensed.table-striped
  thead
    tr
      th
      th Name <br> CAS Number
      th Weight/Formula
      th Structure
      th Library Matches
  tbody
    - @results.each do |result|
      - if result.compound.nil?
        tr: td[colspan="5"] = "Not found: #{result.inchi_key}"
        - next
      tr
        td = link_to "View Spectrum", spectrum_path(result), class: "btn-card",
              'data-turbolinks' => false
        td
          = link_to main_app.url_for result.compound do
            strong
              | #{result.compound.name} (#{get_compound_id(result.compound)})
              hr
              = nah result.compound.cas
        td
          = nah result.compound.average_mass
          hr
          = nah html_formula(result.compound.formula)
        td = moldb_thumbnail(result.compound)
        td = result.matches
