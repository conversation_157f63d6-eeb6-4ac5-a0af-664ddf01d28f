#ms-export-csv.pull-right
  / = form_tag "http://specdb.wishartlab.com/ms/search.csv",
  /     class: "button_to" do |f|
  /   - @search_params.each do |key,value|
  /     = hidden_field_tag key, value
  /   = hidden_field_tag "database", specdb_config(:accessor_database_name)
  /   = submit_tag "Download Results As CSV", class: 'btn btn-primary'
  = link_to "Download Results As CSV", generate_csv_ms_path(results: params.permit(:results), format: "csv"), :class => "btn btn-primary"
= render "/specdb/mzcal/calculator_details_modal"
- @searches.each.with_index do |search,index|
  - query_mass = search.query_mass
  - index += 1
  br
  br
  .panel.panel-default.ms-search-result
    .panel-heading
      .pull-right: small
        | #{glyphicon('info-sign')} Delta = (abs(query mass - adduct mass)/adduct mass)*1000000
      strong MS search for #{search.query_mass} m/z
    .panel-body
      div[id="ms_search_#{index}" class="ms-search-table"
          data-compounds-path="#{url_for([main_app, specdb_config(:compound_class)])}"
          data-mode=params[:mode]
          data-tolerance=params[:tolerance]
          data-database=@database
          data-query-mass=search.query_mass]
        - if search.results.present?
          table.table.table-condensed
            thead
              th Compound
              th Name
              th Formula
              th Monoisotopic Mass
              th Adduct
              th Adduct M/Z
              th Delta (ppm)
              th CCS
            tbody
              - search.results.each do |r|
                - next if r.compound.nil?
                tr
                  td
                    = link_to r.compound_id, [ main_app, r.compound ],
                        class: "btn"
                  td = r.name
                  td = html_formula(r.formula)
                  td = convert_to_signf(r.compound_mass)
                  td = r.adduct
                  td
                    = convert_to_signf(r.adduct_mass)
                    br
                    = link_to_calculator_details(r.compound, r.formula)
                  td = convert_to_ppm(r)
                  - if r.ccs_value.nil?
                    td = 'N/A'
                  - else
                    td = r.ccs_value
        - else
          .alert.alert-warning No results for this query mass
