- if @custom_search_path.present?
  = render @custom_search_path
- else
  .page-header: h1 = title 'Spectra Search <small>Mass Spectrum</small>'
  = render "/specdb/search/navigation", selected: :ms

  - if params[:commit].present?
    = form_toggler

  .specdb-search-form class=(params[:commit].present? ? 'collapse' : '')
    = form_tag ms_search_path, { id: 'ms-search-form', class: 'form-horizontal' } do
      .form-group
        .col-sm-6
          .form-group
            .col-sm-10
              = label_tag :query_masses, 'Query Masses (Da) and Collision Cross Section (Å²):', class: "label-vertical text-area-label"
              = text_area_tag :query_masses, params[:query_masses], class: 'form-control', rows: '12', placeholder: "Enter one mass and CCS value (optional) per line (maximum #{Specdb::MsController::LIMIT_PEAK_COUNT} query masses per request)"
        .col-sm-6
          .form-group
            .col-sm-6
              = label_tag :ms_search_ion_mode, "Ion Mode:", class: "label-vertical"
            .col-sm-4
              = select_tag :ms_search_ion_mode, options_for_select({ 'Positive' => "positive", 'Negative' => "negative", 'Neutral' => 'neutral'}, params[:ms_search_ion_mode]), { class: "form-control" }
          .form-group
            - adducts = selected_adducts(params[:adduct_type], params[:ms_search_ion_mode])
            .col-sm-6
              = label_tag :adduct_type, "Adduct Type:", class: "label-vertical"
            .col-sm-5
              = select_tag :adduct_type, adducts, {multiple: true, class: "form-control ion-select",
                     'data-positive-adducts' => @positive_adducts.to_json,
                     'data-negative-adducts' => @negative_adducts.to_json,
                     'data-neutral-adducts' => @neutral_adducts.to_json,
                     'data-remember-selected' => params[:adduct_type]}
              .help-block
                ' Hold Ctrl (
                = image_tag 'specdb/windows_logo.svg', class: 'windows-logo'
                '  ) or Command (
                = image_tag 'specdb/apple_logo.svg', class: 'mac-logo'
                '  ) to select multiple adducts.
          .form-group
            .col-sm-6
              = label_tag :tolerance, 'Molecular Weight Tolerance ±:', class: "label-vertical"
            .col-sm-3
              = text_field_tag :tolerance, params[:tolerance], class: 'form-control', placeholder: "e.g. 0.05"
            .col-sm-3
              = select_tag :tolerance_units, options_for_select(['Da', 'ppm'], params[:tolerance_units]), { class: "form-control" }
          .form-group  
            .col-sm-6
              = label_tag :ccs_predictors, 'CCS Prediction Method:', class: "label-vertical"
            .col-sm-4
              = select_tag :ccs_predictors, options_for_select(['', 'AllCCS', 'DarkChem', 'DeepCCS'], params[:ccs_predictors]), { class: "form-control" }
          .form-group
            .col-sm-6
              = label_tag :ccs_tolerance, 'Collision Cross Section Tolerance ± (%):', class: "label-vertical"
            .col-sm-4
              = select_tag :ccs_tolerance, options_for_select([['',nil], ['1', '0.01'], ['3', '0.03'], ['5', '0.05'], ['10', '0.10']], params[:ccs_value]), { class: "form-control" }

      - if lookup_context.find_all("/specdb/search/ms/_filter").any?
        = render "/specdb/search/ms/filter"

      hr
      .form-group
        .col-sm-12
          .form-group
            .load-search-buttons
              .col-sm-7
                .col-sm-7
                  = link_to "#{glyphicon(:save)} Load Example with CCS".html_safe, "#", class: "btn btn-default example-loader-ms-ccs", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/ms_ccs.json').html_safe + "'"
                  = link_to "#{glyphicon(:save)} Load Example without CCS".html_safe, "#", style: "margin-top:5px",  class: "btn btn-default example-loader-ms", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/ms.json').html_safe + "'"
                .col-sm-2
                  = button_tag "#{glyphicon(:search)} Search".html_safe, name: :commit, class: 'btn btn-primary search-loader', value: 'Search'
            .reset-button
              .col-sm-6
                .col-sm-6
                  = button_tag "#{glyphicon(:repeat)} Reset".html_safe, :name => 'reset', :id => 'reset', :type => 'reset', :class => 'btn btn-danger'

  - if params[:commit].present?
    hr
    h2#results.pull-left Search Results
    br
    - if @searches.present? && @custom_results_path.present?
      = render @custom_results_path
    - elsif @searches.present?
      = render "results"
    - else
      .no-results Enter at least one query mass to get a result.
