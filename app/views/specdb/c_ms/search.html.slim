.page-header: h1 = title 'Spectra Search <small>GC Mass Spectrum</small>'
= render "/specdb/search/navigation", selected: :gc_ms

- if params[:commit].present?
  = form_toggler

.specdb-search-form class=(params[:commit].present? ? 'collapse' : '')
  = form_tag c_ms_search_path, { method: :get, class: 'form-horizontal' } do
    .form-group
      .col-sm-6
        .form-group
          .col-sm-10
            = label_tag :peaks, 'GC/MS Peak List (m/z & Intensity):', class: 'label-vertical text-area-label'
            = text_area_tag :peaks, params[:peaks], id: 'peaks', class: 'form-control', rows: '10', placeholder: "Enter one mass (m/z) and intensity corresponding to one peak per line. Note also that if only the mass is entered, a default peak intensity of 100 is assumed"
      .col-sm-6
        .form-group
          .col-sm-6
            = label_tag :c_ms_retention_value, 'Retention Index:', class: 'label-vertical'
          .col-sm-4
            = text_field_tag :c_ms_retention_value, params[:c_ms_retention_value], class: 'form-control',
                    placeholder: "e.g. 455.003", id: 'retention'
        #retention-fields[style="display:none"]
          .form-group
            .col-sm-6
              = label_tag :c_ms_retention_type, 'Column Type:', class: 'label-vertical'
            .col-sm-6
              = select_tag :c_ms_retention_type,
                      options_for_select({ "Semi-standard non-polar"=>'ri_semistdnp', "Standard non-polar" => 'ri_stdnp',
                              "Standard polar" => 'ri_stdp' },
                              "ri_semistdnp"), class: "form-control"
          .form-group
            .col-sm-6
              = label_tag :retention_tolerance, 'Retention Index Tolerance (%):', class: 'label-vertical'
            .col-sm-6
              = select_tag :retention_tolerance, options_for_select({"1" => 1, "3" => 3, "5" => 5, "10" => 10}, "3"),
                      class: 'form-control'

          .form-group
            .col-sm-6
              = label_tag :derivatization_type, 'Derivatization Type:', class: 'label-vertical'
            .col-sm-6
              = select_tag :derivatization_type,
                      options_for_select(["No derivatization", "TMS derivatization", "TBDMS derivatization"],
                              params[:derivatization_type]),
                      class: 'form-control'
        .form-group
          .col-sm-6
            = label_tag :mass_charge_tolerence, 'Mass Tolerance ± (Da):', class: 'label-vertical'
          .col-sm-6
            = number_field_tag :mass_charge_tolerence, params[:mass_charge_tolerence],
                    class: 'form-control faux-inline', type: 'number', min: '0', :value => "0.5", :step => "0.1"

    - if lookup_context.find_all("/search/c_ms/_filter").any?
      = render "/search/c_ms/filter"

    hr
    .form-group
      .col-sm-12
        .form-group
          .load-search-buttons
            .col-sm-6
              .col-sm-6
                = link_to "#{glyphicon(:save)} Load RI Example".html_safe, "#", class: "btn btn-default example-loader-ri", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/c_ms_2.json').html_safe + "'"
                = link_to "#{glyphicon(:save)} Load Peak List Example".html_safe, "#", style: "margin-top:5px", class: "btn btn-default example-loader-pl", 'data-turbolinks': false, 'data-example': "'" + (render '/specdb/search/examples/c_ms.json').html_safe + "'"
              .col-sm-2
                = button_tag "#{glyphicon(:search)} Search".html_safe, name: :commit, class: 'btn btn-primary search-loader', value: 'Search'
          .reset-button
            .col-sm-6
              .col-sm-6
                = button_tag "#{glyphicon(:repeat)} Reset".html_safe, :name => 'reset', :id => 'reset', :type => 'reset', :class => 'btn btn-danger'


- if params[:commit].present?
  hr
  h2#results Search Results
  - if @results.present? && @custom_results_path.present?
    = render @custom_results_path
  - elsif @results.present?
    = render "results"
  - else
    .no-results No Results Found.
