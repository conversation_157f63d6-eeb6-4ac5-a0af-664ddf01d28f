.search-spectrum
  p 
    | Your input spectrum is shown on top in 
    span.search-color blue
    | , and the closest matching result spectrum is shown underneath in 
    span.compare-color red
    | . Use the "Compare Spectrum" buttons on the results list below to view and compare different spectra with your input.
  .search-legend
    .legend-item
      .search-color-box
      | Input spectrum
    .legend-item
      .compare-color-box
      | Current comparison result spectrum: 
      span#comparison-compound
  .spectrum-overlay
    = image_tag "loading-line.gif"
  #spectrum-graph

table.table.table-condensed.table-striped.results-table
  thead
    tr
      th Name/CAS Number
      th Derivative <br> Type
      th Molecular Weight <br>/Formula
      th Derivative Molecular Weight <br>/Formula 
      th Structure
      th Retention <br> Index Values
      th Matching Scores <br> (<a href="#" data-toggle="tooltip" title="Proportion of query peaks with matches."> 1 </a>/<a href="#" data-toggle="tooltip" title="Proportion of library spectrum's peaks with matches."> 2 </a>/<a href="#" data-toggle="tooltip" title='Pearson correlation between intensities of paired peaks, where unmatched peaks are paired with zero-intensity "pseudo-peaks".'> 3 </a>)
      th Spectral Display Tools
  tbody
    - first = true
    - @results.each do |result|
      - compound = result.compound
      - next if result.compound.nil? # Shouldn't happen but alas
      tr
        td style="width: 10%;"
          = link_to main_app.url_for compound do
            strong
              | #{compound.name} (#{ get_compound_id(compound) })
              hr
              = nah compound.cas      
        td style="width: 10%;"
          - if result.derivative_type.present?
            / = "#{compound.name}" + " ("+result.derivative_type+")"
            = result.derivative_type
          - else
            = "Underivatized"
        td style="width: 10%;"
            = nah compound.average_mass
            hr
            = nah html_formula(compound.formula)
        td style="width: 10%;"
          - if result.derivative_type.present?
            = nah result.derivative_mw
            hr
            = nah html_formula(result.derivative_formula)
          - else
            = "N/A"
        td style="width: 10%;"
          = moldb_thumbnail(compound)
        td style="width: 10%;"
          - if @retention_value.present?
            - if @retention_type == "ri_semistdnp"
              - if result.ri_semistdnp.present?
                = result.ri_semistdnp
              - else
                = "N/A"

            - elsif @retention_type == "ri_stdnp"
              - if result.ri_stdnp.present?
                = result.ri_stdnp
              - else
                = "N/A"

            - elsif @retention_type == "ri_stdp"
              - if result.ri_stdp.present?
                = result.ri_stdp
              - else
                = "N/A"

          - else 
            = nah "#{result.ri_semistdnp} (Semi-standard non-polar)"
            hr
            = nah "#{result.ri_stdnp} (Standard non-polar)"
            hr
            = nah "#{result.ri_stdp} (Standard polar)" 

        td style="width: 10%;"
          = '%0.2f / %0.2f / %0.2f' % [result.query_matches, result.library_matches, result.correl] 
        td style="text-align: left; width: 10%;"
          div [class="type #{result.type.downcase}"] = result.type 
          = link_to "View Spectrum", spectrum_path(result), class: "btn-card", 'data-turbolinks' => false
          - if first
            = link_to " Current Spectrum", "#", class: "compare btn-card btn-current", 'data-spectrum_id' => result.id, disabled: "disabled", 'spec_name' => "c_ms"
          - else
            = link_to "Compare Spectrum", "#", class: "compare btn-card btn-compare", 'data-spectrum_id' => result.id, 'spec_name' => "c_ms"        
      - if first
        - first = false
        javascript:
          window.compareCandidate = #{ load_compound(result.id, "CMs").to_json.html_safe };

javascript:
  mz_plot("#spectrum-graph", #{ @search_peaks.to_json.html_safe }, ".search-spectrum", { mirror: true });
