module Specdb
  class EiMsController < Specdb::SpectraController


  	def generate_mzml

      spectrum = "Specdb::EiMs".constantize.find(params["id"])

      temp_xml = Tempfile.new(['mzml', '.xml'])
      temp_xml.binmode
      temp_xml.write spectrum.to_mz_ml(request)
      temp_xml.rewind
      temp_xml.close
      send_file temp_xml.path, :type=>"application/xml", :filename => "#{spectrum.compound.name}_#{params["id"]}_ei_ms.mzML"
      
    end
  end
end
