module Specdb
  class SpecdbController < ApplicationController
    helper Specdb::Engine.helpers
    helper Moldbi::StructureResourcesHelper
    include Specdb::RescueHelper
    include Specdb::SpecdbConfigHelper
    helper Specdb::SpecdbConfigHelper

    before_action :set_adduct_variables

    private

    def set_adduct_variables
      # modes = Rails.cache.fetch('modes/adducts', expires_in: 1.day) do
      #   Specdb::Mode.all
      # end
      modes = Specdb::Mode.all
      modes.each do |mode|
        self.instance_variable_set "@#{mode.mode}_adducts", ['Unknown'] + mode.adducts
      end
    end
  end
end
