module Specdb
  class PeaksController < Specdb::SpecdbController 
    respond_to :json
    before_action :get_spectrum_name

    def index
      @spectrum = @spectrum_class.find(params["#{@spectrum_name}_id".to_sym])
      @peaks    = @spectrum.peaks
      respond_with @peaks
    end

    private

    def get_spectrum_name
      @spectrum_class = "Specdb::#{params[:spectrum_type].to_s.classify}".constantize
      @spectrum_name  = params[:spectrum_type]
    end
  end
end
