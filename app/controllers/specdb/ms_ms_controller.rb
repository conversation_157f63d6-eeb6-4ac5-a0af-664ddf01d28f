module Specdb
  class MsMsController < Specdb::SpectraController
    include ApplicationHelper

    def search
      # Allows adding of custom results page by providing the specified file
      resource_name = Specdb.config.compound_class.table_name
      if lookup_context.find_all("/specdbi/ms_ms_search/#{resource_name}/_results.html.slim").any?
        @custom_results_path = "/specdbi/ms_ms_search/#{resource_name}/results"
      end
      
      rescue_from_connection_errors do
        params[:ionization_mode] = params[:ms_ms_search_ion_mode]
        params[:adduct_type] = params[:ms_ms_search_adduct_type]
        if params[:commit].present?
          # Handle file upload or text input for peaks
          peaks_input = get_peaks_input(params)
          @results = @spectrum_result_class.find(:all, :params => params.except(:ms_ms_search_ion_mode, :ms_ms_search_adduct_type, :peaklist_file))
          @search_peaks = parse_peak_list(peaks_input)
        end
      end
    end

    def parse_peak_list(string)
      data = []
      lines = string.split(/\r?\n/)
      lines.each do |line|
        x = line.split(/\s+/)[0].try(:strip).try(:to_f)
        y = line.split(/\s+/)[1].try(:strip).try(:to_f)
        data << {x: (x.nil? ? 0 : x), y: (y.nil? ? 100 : y)}
      end
      return data
    end

    def load_candidate
      # Get the compound data
      compound_data = load_compound(params["id"], "MsMs")

      respond_to do |format|
        format.json {render json: compound_data.to_json }
      end
    end

    def generate_mzml

      spectrum = "Specdb::MsMs".constantize.find(params["id"])

      temp_xml = Tempfile.new(['mzml', '.xml'])
      temp_xml.binmode
      temp_xml.write spectrum.to_mz_ml(request)
      temp_xml.rewind
      temp_xml.close

      send_file temp_xml.path, :type=>"application/xml", :filename => "#{spectrum.compound.name}_lc_msms.mzML"
      
    end

    private

    def get_peaks_input(params)
      # If a file is uploaded, use its content; otherwise use the textarea input
      if params[:peaklist_file].present? && params[:peaklist_file].respond_to?(:read)
        file_content = params[:peaklist_file].read

        # Basic validation
        if file_content.bytesize > 1.megabyte
          flash[:error] = "File size exceeds 1MB limit"
          return params[:peaks]
        end

        file_content
      else
        params[:peaks]
      end
    end

  end
end
