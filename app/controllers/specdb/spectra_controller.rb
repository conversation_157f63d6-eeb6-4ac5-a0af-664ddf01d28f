module Specdb
  class SpectraController < Specdb::SpecdbController
    respond_to :html, :json
    before_action :get_spectrum_name

    def index
      objects = 
        if params[:inchikey]
          @spectrum_class.where :inchi_key => params[:inchikey], 
            :page => params[:page], :per_page => 15
        else
          @spectrum_class.where :page => params[:page], :per_page => 15
        end

      instance_variable_set "@#{@spectrum_name}", objects
      respond_with objects
    end

    def show
      begin
        @spectrum = @spectrum_class.find(params[:id])
        instance_variable_set "@#{@spectrum_name}", @spectrum
      rescue ActiveResource::ResourceNotFound
        render_404
      end
    end
    
    def render_404
      respond_to do |format|
        format.html { render :file => "errors/404.html.erb", :layout => 'application', :status => :not_found }
      end
    end
    # GET or POST ms_ms/search
    def search
      if params[:controller] == "specdb/nmr_two_d"
        # Allows adding of custom results page by providing the specified file
        resource_name = Specdb.config.compound_class.table_name
        if lookup_context.find_all("/specdbi/nmr_two_d_search/#{resource_name}/_results.html.slim").any?
          @custom_results_path = "/specdbi/nmr_two_d_search/#{resource_name}/results"
        end
      end
      
      rescue_from_connection_errors do
        if params[:commit].present?
          @results = @spectrum_result_class.find(:all, :params => params)
        end
      end
    end

    private 

    def get_spectrum_name
      @spectrum_class = "Specdb::#{controller_name.classify}".constantize
      if @spectrum_class != Specdb::EiMs # For now there is no search for EiMs
        @spectrum_result_class = "#{@spectrum_class}Result".constantize
      end
      @spectrum_name  = controller_name.classify.underscore.sub(/specdb\//, "")
    end


  end
end
