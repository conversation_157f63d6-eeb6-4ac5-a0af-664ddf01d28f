module Specdb
  class MsCmmController < Specdb::SpecdbController
    respond_to :html, :json, :csv
    LIMIT_PEAK_COUNT = 300

    def search
      rescue_from_connection_errors {run_ms_cmm_search}
    end

    def run_ms_cmm_search
      # Set up the search params in a hash which will be tranformed to json to post to API
      h = {}
      h.store("chemical_alphabet", params[:chemical_alphabet])
      h.store("modifiers_type", params[:modifiers_type])
      h.store("metabolites_type", params[:metabolites_type])

      # @cmm_database and @masses_mode are predefined attributes
      @cmm_database = ["hmdb"]
      h.store("databases", @cmm_database)
      @masses_mode = 'mz'
      h.store("masses_mode", @masses_mode)

      @ion_mode = params[:ms_search_ion_mode]
      h.store("ion_mode", @ion_mode)

      if params[:adduct_type].present?
        if params[:adduct_type][0].downcase == "unknown"
          params[:adduct_type][0] = "all"
        end
        @adducts = params[:adduct_type]
      end
      h.store("adducts", @adducts)

      if not params[:deuterium].present?
        @deuterium = false
      else
        @deuterium = true
      end
      h.store("deuterium", @deuterium)

      h.store("tolerance", params[:tolerance].to_f)
      h.store("tolerance_mode", params[:tolerance_units])

      # Split the query masses and only take the queries up to our limit (500 entries)
      @masses = params[:masses].to_s.split(/\s+/)[0..LIMIT_PEAK_COUNT]
      @all_masses = params[:all_masses].to_s.split(/\s+/)[0..LIMIT_PEAK_COUNT]
      @retention_times = params[:retention_times].to_s.split(/\s+/)[0..LIMIT_PEAK_COUNT]
      @all_retention_times = params[:all_retention_times].to_s.split(/\s+/)[0..LIMIT_PEAK_COUNT]
      @composite_spectra = make_array_of_arrays_with_mz_and_intensity(params[:composite_spectra].to_s.split(/\r\n/))[0..LIMIT_PEAK_COUNT]
      @all_composite_spectra = make_array_of_arrays_with_mz_and_intensity(params[:all_composite_spectra].to_s.split(/\r\n/))[0..LIMIT_PEAK_COUNT]

      
      h.store("masses", @masses.map!{ |m| m.to_f })
      h.store("all_masses", @all_masses.map!{ |m| m.to_f })
      h.store("retention_times", @retention_times.map!{ |m| m.to_f })
      h.store("all_retention_times", @all_retention_times.map!{ |m| m.to_f })
      h.store("composite_spectra", @composite_spectra)
      h.store("all_composite_spectra", @all_composite_spectra)

      # convert to json and post to API
      post_json = h.to_json
      # puts post_json.to_s
      api_results = Specdb::MsCmmSearch.search(:body => post_json,
                                              :headers => {'Content-Type' => 'application/json', 'Accept' => 'application/json'},
                                              timeout: 200)

      # if there was results, group based on m/z and retention time
      if api_results && api_results["results"].present?
         group_results(api_results)
      end
    end

    def make_array_of_arrays_with_mz_and_intensity(array)
      final_array = nil
      array.map! do |sub_array|
        sub_array = sub_array[1..-2]
        arr = []
        sub_array.split(')(').map! do |sub_sub_array|
          sub_sub_array = sub_sub_array.split(', ')
          arr << {mz: sub_sub_array[0].to_f, intensity: sub_sub_array[1].to_f}
        end
        sub_array = arr
      end
      final_array = array
      return final_array
    end

    def group_results(results)
      results_hash = Hash.new { |h, k| h[k] = [] }
      searches = results["results"]
      number_of_columns = 2
      number_of_rows = searches.size
      searches.each do |record|
        results_hash[[record["RT"], record["EM"]]].push(record)
      end
      @searches = results_hash
    end

  end
end
