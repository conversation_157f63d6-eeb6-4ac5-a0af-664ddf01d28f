module Specdb
  class NmrOneDController < Specdb::SpectraController
    include ApplicationHelper
    respond_to :html, :json, :xml, :nmrml

    # New search URL
    #def search
      ##rescue_from_connection_errors do
        #search_params = {
          #peaks: params[:peaks],
          #nucleus: params[:nucleus],
          #tolerance: params[:cs_tolerance]
        #}

        #@search = Specdb::NmrOneDSearch.create(search_params)
        ##@results = @search.results
      ##end
    #end

    def show
      super
      respond_with(@spectrum)
    end

    def load_candidate
      spectrum = Specdb::NmrOneD.find(params["id"])
      data = {}
      data["compound_name"] = spectrum.compound.name
      data["compound_id"] = spectrum.compound.id
      data["spectrum_id"] = params["id"]
      data["peak_list"] = parse_spectra_one_d(spectrum.peaks)

      respond_to do |format|
        format.json {render json: data.to_json }
      end
    end

    def parse_spectra_one_d(peaks)
      data = []
      peaks.each do |peak|
        data_point = {x: peak.chemical_shift.to_f * -1, y: peak.intensity.to_f}
        data_point[:y] = 100 if data_point[:y].nil? || data_point[:y].zero?
        data << data_point
      end
    end

  end
end
