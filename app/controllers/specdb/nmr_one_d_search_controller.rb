module Specdb
  class NmrOneDSearchController < SpecdbController
    helper SearchHelper

    def self.controller_path
      "specdb/nmr_one_d/search"
    end

    def index
      redirect_to action: 'new'
    end

    def new; end

    # New search URL
    def create
      rescue_from_connection_errors do
        # Build the params for the request
        search_params = {}.tap do |search_hash|
          search_hash[:peaks] = get_peaks_input(params)
          search_hash[:search_type] = params[:search_type]
          search_hash[:intensities] = get_intensities_input(params)
          search_hash[:nucleus] = params[:nucleus]
          search_hash[:cs_tolerance] = params[:cs_tolerance]
          search_hash[:frequency] = params[:frequency]
          search_hash[:database] = params.key?(:database) ? params[:database] : nil
        end
        logger.debug "\n\n\\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"
        @search = Specdb::NmrOneDSearch.create(search_params)
        logger.debug(@search.inspect)
      end
      @spectra_data = params[:peaks]

      return if @no_spectra_connection
      redirect_to action: 'show', id: @search.id, peaks: params[:peaks], intensities: params[:intensities], per_page: 10, page: 1
    end

    def show
      # Allows adding of custom results page by providing the specified file
      resource_name = Specdb.config.compound_class.table_name
      if lookup_context.find_all("/specdbi/nmr_one_d_search/#{resource_name}/_results.html.slim").any?
        @custom_results_path = "/specdbi/nmr_one_d_search/#{resource_name}/results"
      end
      # Check if the search exists
      @search = Specdb::NmrOneDSearch.find(params[:id])
      raise ActiveRecord::RecordNotFound if @search.nil?
      @search.frequency = nil if @search.frequency.present? && @search.frequency.zero? # If frequency is 0 it should actually be nil
      @search_input = ""
      @search.query_spec.each_with_index do |pos, idx|
        @search_input << "#{pos} #{@search.intensities[idx]}\n"
      end
      # Check for the paginate params
      if params_for_paginate.empty?
        redirect_to action: 'show', id: @search.id, per_page: 10, page: 1
      end
      @spectra_data = parse_spectra_one_d(params[:peaks], params[:intensities])
      @results = @search.results(params_for_paginate)
    end

    private

    def params_for_paginate
      params.permit(:page, :per_page).slice(:page, :per_page)
    end

    def parse_spectra_one_d(peaks, intensities)
      data = []

      if intensities.nil? 
        peaks.split(/\r\n/).each do |peak|
          data_point = { x: peak.split()[0].to_f * -1, y: peak.split()[1].to_f }
          data << data_point
        end
      else 
        peak_list = peaks.split(/\r\n/)
        intensity_list = intensities.split(/\r\n/)
        peak_list.zip(intensity_list) do |peak, intensity|
          data_point = { x: peak.to_f * -1, y: intensity.to_f}
          data << data_point
        end
      end
      return data
    end

    def get_peaks_input(params)
      # If a file is uploaded, use its content; otherwise use the textarea input
      if params[:peaks_file].present? && params[:peaks_file].respond_to?(:read)
        file_content = params[:peaks_file].read

        # Basic validation
        if file_content.bytesize > 1.megabyte
          flash[:error] = "Peaks file size exceeds 1MB limit"
          return params[:peaks]
        end

        file_content
      else
        params[:peaks]
      end
    end

    def get_intensities_input(params)
      # If a file is uploaded, use its content; otherwise use the textarea input
      if params[:intensities_file].present? && params[:intensities_file].respond_to?(:read)
        file_content = params[:intensities_file].read

        # Basic validation
        if file_content.bytesize > 1.megabyte
          flash[:error] = "Intensities file size exceeds 1MB limit"
          return params[:intensities]
        end

        file_content
      else
        params[:intensities]
      end
    end

  end
end
