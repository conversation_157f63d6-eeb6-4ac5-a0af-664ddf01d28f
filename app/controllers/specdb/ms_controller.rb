module Specdb
  class MsController < Specdb::SpecdbController
    #Controller for LC-MS
    respond_to :html, :json, :csv

    ALLOWED_DIRECTIONS = %w[up down]
    ALLOWED_COLUMNS    = %w[compound name formula adduct adduct_mass compound_mass delta ccs_value]
    DEFAULT_PER_PAGE   = 10
    DEFAULT_TOLERANCE  = "0.1"
    LIMIT_PEAK_COUNT   = 700

    def search
      # Allows adding of custom search page by providing the specified file
      resource_name = Specdb.config.compound_class.table_name
      if lookup_context.find_all("/specdbi/ms_search/#{resource_name}/_search.html.slim").any?
        @custom_search_path = "/specdbi/ms_search/#{resource_name}/search"
      end

      rescue_from_connection_errors {run_ms_search}
    end

    def run_ms_search
      # sort_order = ALLOWED_DIRECTIONS.include?(params[:d]) ? params[:d] : nil
      # sort_col   = ALLOWED_COLUMNS   .include?(params[:c]) ? params[:c] : nil

      @database = specdb_config(:accessor_database_name)

      # Set up the search parameters
      @search_params = params.to_unsafe_h.slice ( :tolerance_units )
      @search_params[:mode] = params[:ms_search_ion_mode]
      @search_params[:adduct_type] = params[:adduct_type]
      @search_params[:query_organism] = params[:query_organism]

      begin
        # Handle file upload or text input for query masses
        query_masses_text = get_query_masses_input(params)

        # Split the query masses on whitespace and only take the
        # queries up to our limit
        data = []
        lines = query_masses_text.to_s.split(/\r?\n/)
        lines.each do |line|
          puts line
          data << {mass: line.to_s.split(/\s+/)[0], ccs: line.to_s.split(/\s+/)[1]}
        end
      rescue => e
        flash[:error] = e.message
        return
      end
      # @search_params[:query_masses] =
      #   params[:query_masses].to_s.split(/\s+/).
      #   uniq.compact[0..LIMIT_PEAK_COUNT]
      # Set the tolerance to the default tolerance
      @search_params[:tolerance] =
        if params[:tolerance].blank?
          DEFAULT_TOLERANCE
        else
          params[:tolerance]
        end

      @search_params[:ccs_predictors] = params[:ccs_predictors]
      @search_params[:ccs_tolerance] = params[:ccs_tolerance].to_f

      # We limit the number of results we get per peak
      # so that we don't kill the server if someone puts in
      # a huge tolerance
      #@search_params[:per_page] = LIMIT_PER_PEAK

      # Collect the searches for each query mass into an array
      @searches = data.map do |query|
        my_params = @search_params.clone.merge( {:query_masses => query[:mass], :ccs_value => query[:ccs]})
        Specdb::MsSearch.search(my_params)
      end
      # @searches = @search_params[:query_masses].map do |query_mass|
      #   my_params = @search_params.clone.merge( {:query_masses => query_mass} )
      #   Specdb::MsSearch.search(my_params)
      # end
      #collect the sources for each query mass
      #@sources = @search_params[:query_masses].map do |query_mass|
        #puts query_mass
        #my_params = @search_params.clone.merge( {:query_masses => query_mass} )
        #3Specdb::MsSearch.sources(my_params)
      #end

      # Allows adding of custom results page by providing the specified file
      resource_name = Specdb.config.compound_class.table_name
      if lookup_context.find_all("/specdbi/ms_search/#{resource_name}/_results.html.slim").any?
        @custom_results_path = "/specdbi/ms_search/#{resource_name}/results"
      end
    end

    def generate_mzml

      spectrum = "Specdb::SpectrumModel".constantize.find(params["id"])

      temp_xml = Tempfile.new(['mzml', '.xml'])
      temp_xml.binmode
      temp_xml.write spectrum.to_mz_ml(request)
      temp_xml.rewind
      temp_xml.close

      send_file temp_xml.path, :type=>"application/xml", :filename => "#{spectrum.compound.name}_#{params["id"]}_ms.mzML"
      
    end

    def generate_csv
      # rescue_from_connection_errors {run_ms_search}
      @database = specdb_config(:accessor_database_name)
      new_params = params[:results]
      # Set up the search parameters
      @search_params = new_params.slice ( :tolerance_units )
      @search_params[:mode] = new_params[:ms_search_ion_mode]
      @search_params[:adduct_type] = new_params[:adduct_type]
      @search_params[:query_organism] = new_params[:query_organism]

      begin
        # Handle file upload or text input for query masses
        query_masses_text = get_query_masses_input(new_params)

        # Split the query masses on whitespace and only take the
        # queries up to our limit
        data = []
        lines = query_masses_text.to_s.split(/\r?\n/)
        lines.each do |line|
          puts line
          data << {mass: line.to_s.split(/\s+/)[0], ccs: line.to_s.split(/\s+/)[1]}
        end
      rescue => e
        respond_to do |format|
          format.json { render json: { error: e.message }, status: :bad_request }
          format.csv { render plain: "Error: #{e.message}", status: :bad_request }
        end
        return
      end
      # @search_params[:query_masses] =
      #   params[:query_masses].to_s.split(/\s+/).
      #   uniq.compact[0..LIMIT_PEAK_COUNT]
      # Set the tolerance to the default tolerance
      @search_params[:tolerance] =
        if new_params[:tolerance].blank?
          DEFAULT_TOLERANCE
        else
          new_params[:tolerance]
        end

      @search_params[:ccs_predictors] = new_params[:ccs_predictors]
      @search_params[:ccs_tolerance] = new_params[:ccs_tolerance].to_f

      # We limit the number of results we get per peak
      # so that we don't kill the server if someone puts in
      # a huge tolerance
      #@search_params[:per_page] = LIMIT_PER_PEAK

      # Collect the searches for each query mass into an array
      @searches = data.map do |query|
        my_params = @search_params.clone.merge( {:query_masses => query[:mass], :ccs_value => query[:ccs]})
        Specdb::MsSearch.search(my_params)
      end

      respond_to do |format|
        format.json
        format.csv { send_data Specdb::MsCsv.new(@searches).build, :filename => "search.csv"}
      end
    end

    private

    def get_query_masses_input(params)
      # If a file is uploaded, use its content; otherwise use the textarea input
      if params[:peaklist_file].present? && params[:peaklist_file].respond_to?(:read)
        file_content = params[:peaklist_file].read

        # Validate file size (1MB limit)
        if file_content.bytesize > 1.megabyte
          raise "File size exceeds 1MB limit"
        end

        # Validate file content format
        lines = file_content.split(/\r?\n/).reject(&:blank?)

        # Check line count limit
        if lines.length > LIMIT_PEAK_COUNT
          raise "File contains #{lines.length} lines. Maximum allowed is #{LIMIT_PEAK_COUNT} lines."
        end

        # Validate each line format
        lines.each_with_index do |line, index|
          parts = line.strip.split(/\s+/)
          if parts.length > 2
            raise "Invalid format on line #{index + 1}: too many values. Expected mass and optional CCS value."
          end

          parts.each do |part|
            # Check if the part is a valid number (integer or decimal)
            begin
              Float(part)
            rescue ArgumentError
              raise "Invalid number format on line #{index + 1}: '#{part}'"
            end
          end
        end

        file_content
      else
        params[:query_masses]
      end
    end

  end
end
