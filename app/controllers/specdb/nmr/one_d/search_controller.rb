module Specdb
  module Nmr
    module OneD
      class SearchController < SpecdbController
        def index
          redirect_to action: "new"
        end

        def new
        end

        # New search URL
        def create
          #rescue_from_connection_errors do
          search_params = {
            peaks:     params[:peaks],
            nucleus:   params[:nucleus],
            tolerance: params[:cs_tolerance]
          }

          @search = Specdb::NmrOneDSearch.create(search_params) # this complain: unable to convert unpermitted parameters to hash

          redirect_to action: "show", id: @search.id, per_page: 10, page: 1
          #end
        end

        def show
          # Allows adding of custom results page by providing the specified file
          resource_name = Specdb.config.compound_class.table_name
          if lookup_context.find_all("/specdbi/nmr_one_d_search/#{resource_name}/_results.html.slim").any?
            @custom_results_path = "/specdbi/nmr_one_d_search/#{resource_name}/results"
          end

          # Check if the search exists
          @search = Specdb::NmrOneDSearch.find(params[:id])
          raise ActiveRecord::RecordNotFound if @search.nil?

          # Check for the paginate params
          if params_for_paginate.empty?
            redirect_to action: "show", id: @search.id, per_page: 10, page: 1
          end

          @results = @search.results(params_for_paginate)
        end

        private

        def params_for_paginate
          params.slice(:page,:per_page)
        end

      end
    end
  end
end
