module Specdb

  class MzcalController < Specdb::SpecdbController
    layout false
    def calculator
      # Now you can access the compound model from chemdb id
      # Access moldbi functions defined in moldbi
      comp_class = Specdb.config.compound_class
      database_id_column   = Specdb.config.compound_id_column
      compound_name_column = Specdb.config.compound_name_column
      #puts compound_name_column
      compound_name_relation = comp_class.select(compound_name_column).where(database_id_column => params[:id]).first
      @compound_name = compound_name_relation.try(compound_name_column)
      #puts @compound_name
      @formula = params[:chemi_formula]
      @ms_search_compound = comp_class.find_by("#{database_id_column}" => params[:id])
      #mz_cal_monomass = (@ms_search_compound.moldb_mono_mass).to_f
      @mz_cal_values = Specdb::Mzcal.adduct_for_compound(@ms_search_compound)

      #if @ms_search_compound.moldb_adducts.present?
      #@mz_cal_values = @ms_search_compound.contdb_adducts
    end
  end
end