module Specdb
  class CMsController < Specdb::SpectraController
    #Controller for GC-MS
    include ApplicationHelper
    
    def search
      # Allows adding of custom results page by providing the specified file
      resource_name = Specdb.config.compound_class.table_name
      if lookup_context.find_all("/specdbi/c_ms_search/#{resource_name}/_results.html.slim").any?
        @custom_results_path = "/specdbi/c_ms_search/#{resource_name}/results"
      end
      
      rescue_from_connection_errors do
        #params[:mass_charge_tolerence] = 0.1
        # if params[:c_ms_retention_type] == "time"
        #   params[:retention_time] = params[:c_ms_retention_value]
        # elsif params[:c_ms_retention_type] == "index"
        #   params[:retention_index] = params[:c_ms_retention_value]
        # end
        if params[:commit].present?
          @results = @spectrum_result_class.find(:all, :params => params)
          puts("@results = #{@results}")
          # @results = @spectrum_result_class.find(:all, :params => params.except(:c_ms_retention_type, :c_ms_retention_value))
          @search_peaks = parse_peak_list(params[:peaks])
          @derivative_type = params[:derivatization_type]
          puts("@derivative_type = #{@derivative_type}")
          @retention_type = params[:c_ms_retention_type]
          puts("@retention_type = #{@retention_type}")
          @retention_value = params[:c_ms_retention_value]
          puts("@retention_value = #{@retention_value}")
        end
      end
    end

    def load_candidate
      # Get the compound data
      compound_data = load_compound(params["id"], "CMs")
      respond_to do |format|
        format.json {render json: compound_data.to_json }
      end
    end

    def generate_mzml
      spectrum = "Specdb::CMs".constantize.find(params["id"])
      temp_xml = Tempfile.new(['mzml', '.xml'])
      temp_xml.binmode
      temp_xml.write spectrum.to_mz_ml(request)
      temp_xml.rewind
      temp_xml.close
      send_file temp_xml.path, :type=>"application/xml", :filename => "#{spectrum.compound.name}_#{params["id"]}_gc_ms.mzML" 
    end

  end
end
