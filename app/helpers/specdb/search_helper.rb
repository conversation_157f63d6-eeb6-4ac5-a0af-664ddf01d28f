module Specdb
  module Search<PERSON>elper
    SEARCH_TYPE_TEXT="<b>Pure Compound:</b><br>
                      Search against the spectra library using both peak position and peak intensities to identify
                      probable compounds.<br><br>
                      <b>Mixture Search:</b><br>
                      Search against the spectra library to find compounds that may be present
                      in the query spectrum.".freeze

    SPECTRA_LIBRARY_TEXT="The spectrum library to query against.<br>
                          <b>Note:</b> NMR Search only supports searching against
                          our 1D <sup>1</sup>H or 1D <sup>13</sup>C Spectra Libraries".freeze

    TOLERANCE_TEXT="Adjusts the margin of error for matching peak positions, in ppm<br><b>Default:</b> 0.1".freeze

    FREQUENCY_TEXT="Specifying a frequency is recommended to decrease search time and limit redundant results<br>
                    <b>Default:</b> All Frequencies".freeze

    def search_type_text
      SEARCH_TYPE_TEXT
    end

    def spectra_library_text
      SPECTRA_LIBRARY_TEXT
    end

    def tolerance_text
      TOLERANCE_TEXT
    end

    def frequency_text
      FREQUENCY_TEXT
    end

    def form_toggler
      link_to "#{glyphicon(:'chevron-down')} Search options".html_safe,
        '.specdb-search-form',
        class: 'btn btn-primary',
        data: { toggle: 'collapse'},
        aria: { expanded: "false", controls: ".specdb-search-form" }
    end

    def selected_adducts(adduct_type, mode='positive')
      mode ||= 'positive'
      return adduct_type if adduct_type.present?
      case mode
      when 'positive'
        @positive_adducts
      when 'negative'
        @negative_adducts
      when 'neutral'
        @neutral_adducts
      end
    end


    def convert_to_signf(result)
      four_dec_digit = number_with_precision(result, precision: 4).to_f
      if four_dec_digit.to_s.split(".")[1].length < 4
        x = four_dec_digit.to_s+"0"
        return x
      else
        return four_dec_digit
      end
    end

  def calculator_details_modal
    render 'specdb/mzcal/calculator_details_modal'
  end

  def link_to_calculator_details(compound, formula)
    link_to "m/z calculator".html_safe,
      calculator_mzcal_path(:id => compound, :chemi_formula => formula),
      data: { toggle: 'modal', target: '#calculator-details' },
      class: 'btn-calculator-details'
  end

  def convert_to_ppm(result)
    #delta = convert_to_signf(result.delta)
    delta = result.delta.to_f
    adct_mass = result.adduct_mass.to_f
    ppm = (delta/adct_mass)*1000000
    return ppm.round
  end








  end
end
