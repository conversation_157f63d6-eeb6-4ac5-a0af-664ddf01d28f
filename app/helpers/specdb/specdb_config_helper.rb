module Specdb
  module SpecdbConfigHelper

    def has_specdb_config?
      instance_variable_defined?(:@specdb_config)
    end

    # Allow for setting dynamic configuration within
    # the application controller
    def specdb_config(variable_name)
      if has_specdb_config? && @specdb_config.has_key?(variable_name)
        @specdb_config[variable_name]
      else
        Specdb.config.send(variable_name)
      end
    end

    def get_compound_id(compound)
      id_column = specdb_config(:compound_id_column)
      compound.try(id_column) || compound.try(:to_param)
    end

  end
end
