module Specdb
  module Routes<PERSON><PERSON><PERSON>
    def spectrum_path(spectrum)
      path_method = spectrum.class.to_s.underscore.sub("specdb/", "specdb_").sub("_result","") + "_path"
      send path_method, spectrum
    end

    def specdb_nmr_one_d_search_path(*args); specdb.nmr_one_d_search_path(*args); end
    def specdb_nmr_one_d_peak_assignments_path(*args); specdb.nmr_one_d_peak_assignments_path(*args); end
    def specdb_nmr_one_d_peaks_path(*args); specdb.nmr_one_d_peaks_path(*args); end
    def specdb_nmr_one_d_index_path(*args); specdb.nmr_one_d_index_path(*args); end
    def specdb_nmr_one_d_path(*args); specdb.nmr_one_d_path(*args); end
    def specdb_nmr_two_d_search_path(*args); specdb.nmr_two_d_search_path(*args); end
    def specdb_nmr_two_d_peak_assignments_path(*args); specdb.nmr_two_d_peak_assignments_path(*args); end
    def specdb_nmr_two_d_peaks_path(*args); specdb.nmr_two_d_peaks_path(*args); end
    def specdb_nmr_two_d_index_path(*args); specdb.nmr_two_d_index_path(*args); end
    def specdb_nmr_two_d_path(*args); specdb.nmr_two_d_path(*args); end
    def specdb_c_ms_search_path(*args); specdb.c_ms_search_path(*args); end
    def specdb_c_ms_peak_assignments_path(*args); specdb.c_ms_peak_assignments_path(*args); end
    def specdb_c_ms_peaks_path(*args); specdb.c_ms_peaks_path(*args); end
    def specdb_c_ms_index_path(*args); specdb.c_ms_index_path(*args); end
    def specdb_c_ms_path(*args); specdb.c_ms_path(*args); end
    def specdb_ei_ms_peaks_path(*args); specdb.ei_ms_peaks_path(*args); end
    def specdb_ei_ms_index_path(*args); specdb.ei_ms_index_path(*args); end
    def specdb_ei_ms_path(*args); specdb.ei_ms_path(*args); end
    def specdb_ms_ms_search_path(*args); specdb.ms_ms_search_path(*args); end
    def specdb_ms_ms_peak_assignments_path(*args); specdb.ms_ms_peak_assignments_path(*args); end
    def specdb_ms_ms_peaks_path(*args); specdb.ms_ms_peaks_path(*args); end
    def specdb_ms_ms_index_path(*args); specdb.ms_ms_index_path(*args); end
    def specdb_ms_ms_path(*args); specdb.ms_ms_path(*args); end
    def specdb_ms_search_path(*args); specdb.ms_search_path(*args); end
    def specdb_ms_cmm_search_path(*args); specdb.ms_cmm_search_path(*args); end
    def specdb_statistics_path(*args); specdb.statistics_path(*args); end
    def specdb_statistic_path(*args); specdb.statistic_path(*args); end
    def specdb_ms_ir_peaks_path(*args); specdb.ms_ir_peaks_path(*args); end
    def specdb_ms_ir_index_path(*args); specdb.ms_ir_index_path(*args); end
    def specdb_ms_ir_path(*args); specdb.ms_ir_path(*args); end
  end
end
