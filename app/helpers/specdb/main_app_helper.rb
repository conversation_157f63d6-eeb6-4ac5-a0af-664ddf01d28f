module Specdb
  module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    
    def specdb_spectra_list(compound)
      if lookup_context.find_all("/specdbi/_spectra_list").any?
        render '/specdbi/spectra_list', compound: compound
      else
        render '/specdb/spectra_list', compound: compound
      end
    end

    def specdb_spectra_list_separate(compound)
      if lookup_context.find_all("/specdbi/_spectra_list_separate").any?
        render '/specdbi/spectra_list_separate', compound: compound
      else
        render '/specdb/spectra_list_separate', compound: compound
      end
    end

    def specdb_spectra_list_index(compound)
      if lookup_context.find_all("/specdbi/_spectra_list_index").any?
        render '/specdbi/spectra_list_index', compound: compound
      else
        render '/specdb/spectra_list_index', compound: compound
      end
    end

    # Sort spectra into a hash, by SpecDB type
    def sorted_spectra(spectra)
      sorted_spectra = {}
      sorted_spectra[:gcms] = []
      sorted_spectra[:predicted_gcms] = []
      sorted_spectra[:msms] = []
      sorted_spectra[:predicted_msms] = []
      sorted_spectra[:eims] = []
      sorted_spectra[:nmr1d] = []
      sorted_spectra[:nmr2d] = []
      sorted_spectra[:msir] = []

      spectra.each do |spectrum|
        if spectrum.instance_of?(Specdb::CMs)
          if spectrum.spectrum_type =~ /^Predicted/
            sorted_spectra[:predicted_gcms] << spectrum
          else
            sorted_spectra[:gcms] << spectrum
          end
        elsif spectrum.instance_of?(Specdb::MsMs)
          if spectrum.spectrum_type =~ /^Predicted/
            sorted_spectra[:predicted_msms] << spectrum
          else
            sorted_spectra[:msms] << spectrum
          end
        elsif spectrum.instance_of?(Specdb::EiMs)
          sorted_spectra[:eims] << spectrum
        elsif spectrum.instance_of?(Specdb::NmrOneD)
          sorted_spectra[:nmr1d] << spectrum
        elsif spectrum.instance_of?(Specdb::NmrTwoD)
          sorted_spectra[:nmr2d] << spectrum
        elsif spectrum.instance_of?(Specdb::MsIr)
          sorted_spectra[:msir] << spectrum
        end
      end

      sorted_spectra
    end

    # Render navigation list options for searching (usually from
    # main application under "Search tab").
    def specdb_search_links
      render '/specdb/navigation_search_links'
    end

    # not used anymore
    def specdb_nmr_search_links
      render :partial => '/specdb/nmr_navigation_search_links', :locals => {:nmr => true}
    end
    # this is for alberto's program
    def specdb_cmm_search_link
      render '/specdb/cmm_search_link'
    end
  end
end
