require 'json'
require 'open-uri'

module Specdb
  module <PERSON><PERSON>el<PERSON>
    # Returns the path to get the data for drawing
    # a spectrum from the SpecDB server
    def data_url_for(spectrum)
      "/spectra/#{Spectrum.collection_name}/#{spectrum.class.collection_name}/#{spectrum.id}"
    end

    def specdb_download_nmr_one_d_search_url(nmr_one_d_search)
      "#{SPECDB_API.options[:url]}/nmr_one_d/search/#{nmr_one_d_search.id}.csv"
    end

    def graph(spectrum,title=nil)
      additional_data =
        if spectrum.respond_to?(:nucleus_x)
          "data-nucleus-x='#{spectrum.nucleus_x}' data-nucleus-y='#{spectrum.nucleus_y}'"
        else
          ""
        end

      s = "<div class='spectrum-chart #{spectrum.class.element_name}'
           data-url='#{data_url_for spectrum}' #{additional_data}
           data-title='#{title}' >"

      s += spectrum.peaks.map do |peak|
        assignment = peak.assignments.first
        next if assignment.nil?

        if assignment.image_url.present?
          "<div id='assignment-#{assignment.id}' class='peak-assignment hidden'>
          <img src='#{assignment.image_url}' ></img></div>"
        else
          "<div id='assignment-#{assignment.id}' class='peak-assignment hidden'>
            <img src='#{Specdb.config.site}/#{assignment.path}' ></img></div>"
        end
      end.join("\n")

      s += "</div>"

      s.html_safe
    end

    def show_specdb_header(title, colspan=2)
      name = title.gsub(/\W/, '_').downcase
      "<tr id=\"#{name}\"><th class=\"divider\" colspan=\"#{colspan}\">#{title}</th></tr>".html_safe
    end

    def has_attr(object,attribute)
      object.respond_to?(attribute) && !object.send(attribute).blank?
    end

    def d3_url
      "http://d3js.org/d3.v2.min.js"
    end

    def specdb_javascript_url
      URI.join(Specdb.config.site, "assets/spectra.js").to_s
    end

    def float_or_na(value)
      value ||= 'N/A'
      value == 'N/A' ? value : sprintf("%.2f", value)
    end

    # Turn the peak list into a hash for graphing
    def parse_spectra(peaks, filter)
      data = []
      peaks.each do |peak|
        if peak.assignments.first.present?
          molecular_formula = [peak.assignments.first.formula]
          # Store url of assigned fragment structure image, if it exists
          if peak.assignments.first.image_url.present?
            structures = [peak.assignments.first.image_url]
            # json_link = peak.assignments.first.image_url[0..-11].to_s + ".json"
            # begin
            #   json_data = JSON.load(open(json_link))
            #   molecular_formula = json_data["formula"]
            # rescue OpenURI::HTTPError
            #   molecular_formula = nil
            # end
          else
            structures = [Specdb.config.site + peak.assignments.first.path]
          end
        else
          structures = []
          molecular_formula = nil
        end
        data_point = {formula: molecular_formula, x: peak.mass_charge.to_f, y: peak.intensity.to_f, structures: structures}
        data << data_point
      end
      return data
    end

    # Turn the peak list into a hash for graphing
    def parse_spectra_one_d(peaks, filter)
      data = []
      peaks.each do |peak|
        if peak.assignments.first.present?
          molecular_formula = [peak.assignments.first.formula]
          # Store url of assigned fragment structure image, if it exists
          if peak.assignments.first.image_url.present?
            structures = [peak.assignments.first.image_url]
            # json_link = peak.assignments.first.image_url[0..-11].to_s + ".json"
            # begin
            #   json_data = JSON.load(open(json_link))
            #   molecular_formula = json_data["formula"]
            # rescue OpenURI::HTTPError
            #   molecular_formula = nil
            # end
          else
            structures = [Specdb.config.site + peak.assignments.first.path]
          end
        else
          structures = []
          molecular_formula = nil
        end
        data_point = {formula: molecular_formula, x: peak.chemical_shift.to_f*-1, y: peak.intensity.to_f, structures: structures}
        data << data_point
      end
      return data
    end

    def pubmed_link_or_ref_text(reference)
      if reference.pubmed_id.present?
        bio_link_out :pubmed, reference.pubmed_id
      elsif reference.ref_text.present?
        reference.ref_text
      else
        'Not Available'
      end
    end

    def parse_peak_list(string)
      data = []
      lines = string.split(/\r?\n/)
      lines.each do |line|
        x = line.split(/\s+/)[0].try(:strip).try(:to_f)
        y = line.split(/\s+/)[1].try(:strip).try(:to_f)
        data << {x: (x.nil? ? 0 : x), y: (y.nil? ? 100 : y)}
      end
      return data
    end

    def load_compound(id, type)
      spectrum = "Specdb::#{type}".constantize.find(id)
      data = {}
      data["compound_name"] = spectrum.compound.name
      data["compound_id"] = spectrum.compound.id
      data["spectrum_id"] = id
      if type == "NmrOneD"
        data["peak_list"] = parse_spectra_one_d(spectrum.peaks, false)
      else 
         data["peak_list"] = parse_spectra(spectrum.peaks, false)
      end
      return data
    end

    ##
    # Searches and for useful documents for a spectra and returns them inside an ordered list
    #
    # Params:
    # +spectrum+:: spectrum object to search
    #
    # Notes:
    # Modify the order array to change the files/order that are always displayed. Matches are not case sensitive.
    # Modify the extra array to change the files/order of files that are displayed if they exist. Not case sensitive.
    # Use the desc_map array to change the way the file name is displayed
    #
    # Arrays inside the order/extra arrays denote file names that are substitutable for one another (only one will be displayed),
    # with the lower indexed names receiving priority (if they exist).
    def downloads_list(spectrum)
      order = [["List of chemical shift values for the spectrum", "Peak List"], "Peak Assignments" ,
               ["Spectra image with peak assignments", "Spectra image with Assignments"], "Raw Spectrum Image",
               ["nmrML for JSViewer", "nmrML"],
               ["JCAMP-DX File", "JCAMP file"],"Raw Free Induction Decay file for spectral processing", "Validation Report"]

      extra = []

      desc_map = {'nmrML' => 'nmrML File', 'JCAMP file' => 'JCAMP-DX File',
                  'Spectra image with peak assignments' => 'Spectra Image with Peak Assignments',
                  'Raw Free Induction Decay file for spectral processing' =>
                    get_fid_sentence(spectrum),
                  'nmrML for JSViewer' => 'nmrML File',
      }

      downloads = []
      documents = spectrum.documents
      http = Net::HTTP.new Specdb.config.site.split('//')[1].chomp('/')

      [order, extra].each_with_index do |arr, arr_num|
        arr.each do |desc|
          if desc.instance_of? Array
            index = 0
            doc = []
            while doc.empty? && index <  desc.length
              doc = documents.select {|d| d.description.downcase.include? desc[index].downcase}
              index +=1
            end
            if doc.empty?
              index = 0
              display_name = "#{desc_map[desc[index]] || desc[index]}"
              downloads.append([display_name, nil, nil]) unless arr_num == 1
            else
              index -= 1
              extension = doc[0].name.split('.')[1].upcase
              display_name = "#{desc_map[desc[index]] || desc[index]} (#{extension})"
              file_size = number_to_human_size(http.request_head(doc[0].url)['content-length'].to_i)
              downloads.append([display_name, doc[0], file_size])
            end
          else
            doc = documents.select {|d| d.description.downcase.include? desc.downcase}
            if doc.empty?
              display_name = "#{desc_map[desc]  || desc}"
              downloads.append([display_name, nil, nil]) unless arr_num == 1
            else
              display_name = "#{desc_map[desc]  || desc} (#{doc[0].name.split('.')[1].upcase})"
              file_size = number_to_human_size(http.request_head(doc[0].url)['content-length'].to_i) unless doc.empty?
              downloads.append([display_name, doc[0], file_size])
            end
          end
        end
      end
      downloads
    end



    ##
    # (Temporary?) Method to generate a well-formatted downloads list for spectra without filtering the files
    # See ApplicationHelper::downloads_list for filtered lists
    #
    # params:
    # +spectrum+:: spectrum object to retrieve documents from
    def unrestricted_downloads_list(spectrum)
      downloads = []
      http = Net::HTTP.new Specdb.config.site.split('//')[1].chomp('/')
      spectrum.documents.each do |doc|
        display_name = "#{doc.description} (#{doc.name.split('.')[1].upcase})"
        file_size = number_to_human_size(http.request_head(doc.url)['content-length'].to_i)
        downloads.append([display_name, doc.url, file_size])
      end

      downloads
    end

    # Adds the instrument type to FID file descriptions
    # @param [spectrum] spectrum to produce FID file name for
    # @return [String] Name to be displayed for FID file
    def get_fid_sentence(spectrum)
      sentence = "Raw Free Induction Decay (FID) File for Spectral Processing"
      sentence += " (#{spectrum.instrument_type})" unless spectrum.instrument_type.blank?
    end

  end
end
