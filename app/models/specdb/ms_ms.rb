module Specdb
  class MsMs < SpectrumModel
    include Specdb::MzMl

    self.collection_name = 'ms_ms'

    def spectrum_type
      text = "#{predicted ? "Predicted " : "" }LC-MS/MS Spectrum - #{instrument_type} #{collision_energy_voltage.present? ? collision_energy_voltage.to_s + "V" : collision_energy_level}, #{ionization_mode}"
      # FIXME: quick hack to add Annotated to Quattro spectra. This suffix should only be added if the peaks do have assignments
      text += " (Annotated)" if instrument_type =~ /Quattro/
      text
    end

    class Document < SpecdbResource 
    end

    class Reference < SpecdbResource
    end
  end
end
