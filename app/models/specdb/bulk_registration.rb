module Specdb
  class BulkRegistration
    def self.register_items(secret)

      klass            = Specdb.config.compound_class
      database_name    = Specdb.config.accessor_database_name
      id_getter        = Specdb.config.compound_id_getter
      id_column        = Specdb.config.compound_id_column
      inchi_key_column = Specdb.config.inchi_key_column

      item_name = klass.name.downcase.pluralize
      puts "Registering InChIKeys for #{item_name}, database_name: #{database_name}"

      registration_list = klass.select("#{id_column}, #{inchi_key_column}").
        where("#{inchi_key_column} is not null").map do |item|
          inchi_key = item.send(inchi_key_column).sub(/inchikey=/i,"").strip
          next unless /^[A-Z]{14}-[A-Z]{10}-[A-Z]$/.match(inchi_key)
          "#{item.send(id_getter)}\t#{inchi_key}"
      end.compact

      # TODO get this working with a tempfile instead of sticking it into the params
      #registration_list_file = Tempfile.new("registrationlist"){|f| f.puts registration_list }
      puts "#{registration_list.count} #{item_name} to be registered"

      conn = Faraday.new(:url => Specdb.config.site) do |faraday|
        faraday.request  :url_encoded             # form-encode POST params
        faraday.response :logger                  # log requests to STDOUT
        faraday.adapter  Faraday.default_adapter  # make requests with Net::HTTP
      end
      conn.post '/registrations/bulk', {
        'registration[secret]' => secret,
        'registration[database_name]' => database_name,
        'registration[registration_list]' => registration_list.join("\n")
      }
    end
  end
end
