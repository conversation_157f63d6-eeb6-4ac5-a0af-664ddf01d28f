module Specdb
  class NmrOneDSearch
    include Her::Model
    use_api SPECDB_API

    collection_path 'nmr_one_d/search'

    class Result
      include Her::Model
      use_api SPECDB_API
      collection_path 'nmr_one_d/search/:nmr_one_d_search_id/results'

      def compounds(options={})
        return [] if self.database_ids.nil?
        database = options[:accessor_database_name] || Specdb.config.accessor_database_name
        compound_class = options[:compound_class] ||  Specdb.config.compound_class
        compound_id_column = options[:compound_id_column] || Specdb.config.compound_id_column
        @compounds ||= begin
           ids = self.database_ids[database]
           compound_class.where(compound_id_column => ids) || []
        end
      end

      def any_database_id
        return nil if self.database_ids.nil? || self.database_ids.first.nil?
        self.database_ids.first.flatten.last
      end

      def compound
        return nil if self.compounds.nil?
        self.compounds.first
      end

      def compound_id
        return nil if self.compound.nil?
        self.compound.id
      end

      def compound_name
        return nah if self.compound.nil?
        name = self.compound.send(Specdb.config.compound_name_column)
        if name.blank? then self.iupac else name end
      end
    end
    #has_many :results

    def results(options={})
        my_result = Result.where(nmr_one_d_search_id: self.id).where(options)
        if options.include?(:page) || options.include?(:per_page)
          Kaminari.paginate_array(my_result, total_count: self.total_count).
            page(options[:page]).per(options[:per_page])
        else
          my_result
        end
    end



    # Call MsSearch.search to get an MsSearch result back, for example:
    #
    #   Specdb::NmrOneDSearch.search :query_masses => '175.01',
    #     :mode      => 'positive',
    #     :tolerance => '0.1',
    #
    # Returns an array of search results
    #
    def self.search(options)
      search = self.class.create(options)
      return search
    end


#    def as_json(*args)
#      {
#        #:query_mass => self.query_mass,
#        #:sort_order => @sort_order, 
#        #:sort_col   => @sort_col,
#        #:Result     => 'OK',
#        #:Records    => self.results.as_json(*args),
#        #:TotalRecordCount => self.total
#        
#        #:sEcho => 10,
#        #:iTotalRecords => "57",
#        #:iTotalDisplayRecords => "48",
#        #:aaData => self.results.as_json(*args)
#      }
#    end

    # *Result* contains information about each matching adduct, the search
    # method returns an list of this type of object
    #
    # Here is an example of the attributes for a result object:
    #
    #   "formula"=>"C2H4O3", 
    #   "compound_mass"=>"76.016044", 
    #   "adduct"=>"2M+Na", 
    #   "adduct_type"=>"+", 
    #   "adduct_mass"=>"175.021306", 
    #   "delta"=>"0.021296", 
    #   "compound_id"=>"HMDB00115",
    #   "name"=>"Glycolic acid"
    #
    # You can also get the related compound (with only the id
    # and name column loaded) for example:
    #   
    #   result.compound
    #   # => <Metabolite id: "HMDB00115", common_name: "Glycolic acid">
    #
    # NOTE Currently the results get the compound name from the local database
    # rather than from Specdb
    #


  end
end
