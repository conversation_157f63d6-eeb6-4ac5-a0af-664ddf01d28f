module Specdb
  class SpectrumCollection < Delegator
    def initialize(obj)
      super                # pass obj to Delegator constructor, required
      @spectrum_list = obj # store obj for future use
    end

    def __getobj__
      @spectrum_list 
    end

    def __setobj__(obj)
      @spectrum_list = obj 
    end

    def group_by_spectrum_type
      @group_by_spectrum_type ||= 
				begin 
					grouped = @spectrum_list.group_by do |spectrum|
						spectrum.spectrum_type
					end
					HashWithIndifferentAccess.new grouped
				end
    end

  end
end
