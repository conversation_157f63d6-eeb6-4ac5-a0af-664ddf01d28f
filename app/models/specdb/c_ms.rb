module Specdb
  class CMs < SpectrumModel
    include Specdb::MzMl
    self.collection_name = 'c_ms'

    def spectrum_type
      name = "MS Spectrum"
      name = "#{chromatography_type}-" + name if chromatography_type
      name = "Predicted "+ name if predicted
      name = name + " - #{instrument_type}" if instrument_type    
      name = name + " (#{derivative_type})" if derivative_type
      name = name + " (Non-derivatized)" if !derivative_type
      name = name + " - 70eV, Positive" if predicted

      name
    end

    # This format is demand by <PERSON><PERSON> and <PERSON> approved (20207-30)
    # This method only used at _spectra_list_data_c_ms.html.slim
    def special_spectrum_type
      name = "<strong>#{compound.try(:name)}</strong>, "
      name = name + "<strong>#{derivative_type}</strong>, " if derivative_type
      name = name + "<strong>non-derivatized</strong>, " if !derivative_type

      if predicted
        name = name + "Predicted "
      end

      if chromatography_type
        name = name + "#{chromatography_type}-MS Spectrum"
      else
        name = name + "MS Spectrum"
      end

      name = name + " - 70eV, Positive" if predicted

      name
    end

    def derivative_inchikey
      if self.derivative_smiles.nil?
        return nil
      end
      self.convert_smiles_to_inchikey(self.derivative_smiles)
    end

    def derivative_iupac_name
      if self.derivative_smiles.nil?
        return nil
      end
      self.convert_smiles_to_iupac_name(self.derivative_smiles)
    end

    def convert_smiles_to_iupac_name(smiles)
      name_ = nil
      begin
        response = RestClient.post( "http://jchem:<EMAIL>/jchem/rest-v0/util/calculate/stringMolExport", { structure: smiles, parameters: "name"} )
        name_ = response.body
      rescue Exception => e
        name_ = nil
      end
      name_
    end

    def convert_smiles_to_inchikey(smiles)
      name_ = nil
      begin
        response = RestClient.post( "http://jchem:<EMAIL>/jchem/rest-v0/util/calculate/stringMolExport", { structure: smiles, parameters: "inchikey"} )
        name_ = response.body
      rescue Exception => e
        name_ = nil
      end
      name_
    end

    class Document < SpecdbResource 
    end

    class Reference < SpecdbResource
    end

  end
end
