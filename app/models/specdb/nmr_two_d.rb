module Specdb
  class NmrTwoD < SpectrumModel

    self.collection_name = 'nmr_two_d'

    def spectrum_type
      nucleus_x_new = nucleus_x ? nucleus_x.gsub(/(\d+)/, '<sup>\0</sup>') : ""
      nucleus_y_new = nucleus_y ? nucleus_y.gsub(/(\d+)/, '<sup>\0</sup>') : ""
      solvent_new = solvent ? (subscript_solvent solvent) : ""
      spectrum_source = '<b>experimental</b>'.html_safe
      experiment = get_experiment_type
      if frequency && solvent
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment} NMR Spectrum (2D, #{frequency}, " \
        "#{solvent_new}, #{spectrum_source})".html_safe
      elsif frequency
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment} NMR Spectrum (2D, #{frequency}, #{spectrum_source})".html_safe
      elsif solvent
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment} NMR Spectrum (2D, #{solvent}, #{spectrum_source})".html_safe
      else
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment} 2D NMR Spectrum (#{spectrum_source})".html_safe
      end
    end

    def as_json(options)
      json_file = self.documents.find{|x| x.description == '2D JSON for JSpectraViewer' }
      if json_file
        open(json_file.url).read
      end
    end

    def get_experiment_type
      return '' unless notes
      begin
        return "-#{notes}" unless notes.include?('-')
        # This is needed for some two d spectra in np-mrd but not hmdb
        # return "-#{notes.split(' ')[1].split('-')[1]}"
      rescue StandardError
        ''
      end
    end

    class Document < SpecdbResource 
    end

    class Reference < SpecdbResource
    end

  end
end
