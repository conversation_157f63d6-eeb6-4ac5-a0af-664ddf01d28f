module Specdb
  class NmrOneD < SpectrumModel

    self.collection_name = 'nmr_one_d'

    def spectrum_type
      nucleus_new = nucleus.gsub(/(\d+)/, '<sup>\0</sup>')
      solvent_new = solvent ? (subscript_solvent solvent) : ""
      if structure_id && !frequency.nil?
        if frequency =~ /MHz/
          name = nucleus_new.html_safe + " NMR Spectrum (1D, #{frequency}"
        else
          name = nucleus_new.html_safe + " NMR Spectrum (1D, #{frequency} MHz"
        end
      else
        name = nucleus_new.html_safe + " NMR Spectrum (1D"
      end

      unless solvent_new.empty?
        name += ", #{solvent_new}".html_safe
      end


      if predicted && predicted != 0
        name + ", predicted)"
      else
        name + ", <b>experimental</b>)".html_safe
      end

    end

    # Returns data in a format for the spectra viewer scaling the maximum to height to 1
    def as_json(options)
      max_intensity = self.peaks.map {|p| p.intensity.to_f }.max
      width = nucleus == '1H' ? 0.002 : 0.005
      my_peaks = self.peaks.map do |peak|
        intensity = peak.intensity.to_f / max_intensity
        {center: peak.chemical_shift.to_f, amplitude: intensity, width: width}
      end
      {peaks: my_peaks}
    end

    def to_nmrml
      nmrml = self.documents.find{|x| /nmrML/i.match x.description }
      if nmrml
        open(nmrml.url).read
      end
    end

    class Document < SpecdbResource 
    end

    class Reference < SpecdbResource
    end

  end
end
