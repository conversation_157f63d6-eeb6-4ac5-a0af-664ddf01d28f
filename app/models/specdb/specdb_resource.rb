module Specdb
  class SpecdbResource < ActiveResource::Base 
    cattr_accessor :accessor_database
    self.timeout = 60

    # Cache the resources in the local filesystem
    # if Rails.env.development?
    #   cached_resource \
    #     :cache => ActiveSupport::Cache::FileStore.new(Specdb.config.cache_folder)
    # end

    self.site     = Specdb.config.site
    self.user     = Specdb.config.user
    self.password = Specdb.config.password
    self.format   = Specdb.config.format
    self.accessor_database = Specdb.config.accessor_database_name

    # Don't need want the root in the json for the API
    self.include_root_in_json = false

    # By default set all calls to find to include the database name
    # in the parameters
    def self.find_every(options)
      super set_defaults(options)

    end

    def self.find_one(options)
      super set_defaults(options)
    end

    def self.set_defaults(options)
      options.tap do |o|
        # puts o
        o[:params] ||= Hash.new 
        if o[:params][:query_organism]
          o[:params][:database] = o[:params][:query_organism]
        else
          o[:params][:database] = self.accessor_database
        end
      end
    end
  end
end
