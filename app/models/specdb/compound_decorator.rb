module Specdb
  class CompoundDecorator < Delegator
    # This class wraps the compound class objects when they are returned 
    # from Specdb::Spectrum classes, this allow us to add a single way
    # to access mold_methods or non-moldb methods if they are not present
    def initialize(obj)
      super                  # pass obj to Delegator constructor, required
      @delegate_compound_obj = obj # store obj for future use
    end

    def __getobj__
      @delegate_compound_obj # return object we are delegating to, required
    end

    def __setobj__(obj)
      @delegate_sd_obj = obj # change delegation object 
    end


    # Make the moldb_ methods callable without the
    # moldb_ prefix 
    def method_missing(method,*args)
      if !/^moldb_/.match(method.to_s) && self.respond_to?("moldb_#{method}")
        __getobj__.send "moldb_#{method}", *args
      else
        __getobj__.send method, *args
      end
    end

    # Make respond_to? work for the moldb_ methods without the
    # moldb_ prefix 
    def respond_to?(method,*args)
      __getobj__.respond_to?(method,*args) || __getobj__.respond_to?("moldb_#{method}",*args)
    end

  end
end
