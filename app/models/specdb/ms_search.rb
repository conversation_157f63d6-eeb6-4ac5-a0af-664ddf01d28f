module Specdb
  class MsSearch < SpecdbResource
    self.collection_name = 'ms/search'

    # NOTE Don't call this method directly, use the search method instead
    #def self.find( *args )

      #if args.last.is_a? Hash
        #args.last[:params] ||= Hash.new
        ## set to default database unless it is already set
        #args.last[:params][:database] ||= Specdb.config.accessor_database_name
      #else
        #options = {params: {database: Specdb.config.accessor_database_name}}
        #args << options
      #end

      #super *args
    #end

    # Call MsSearch.search to get an MsSearch result back, for example:
    #   
    #   Specdb::MsSearch.search query_masses: '175.01',
    #     mode:      'positive',
    #     tolerance: '0.1',
    #     per_page:  1000,
    #     adduct_type: 'M+H'
    #
    # Returns an array of search results
    #
    def self.search( params, specdb_options={} )
      # set the database param
      params[:database] = specdb_options[:accessor_database_name] || Specdb.config.accessor_database_name
  
      # Do the search
      ms_search = self.find(:all, params: params).first
      # Grab the id_column and name_column from the config file
      id_column   = specdb_options[:compound_id_column]   || Specdb.config.compound_id_column
      name_column = specdb_options[:compound_name_column] || Specdb.config.compound_name_column
      # Grab the matching compound ids
      compound_ids = ms_search.results.map(&:compound_id)

      # Load the matching compounds in one query and
      # put them into a Hash
      compoud_class = specdb_options[:compound_class] || Specdb.config.compound_class

      compounds =
        compoud_class.exported.
        # Select only the columns we need so that it loads
        # faster
        select("#{id_column}, #{name_column}").
        # Grab the compounds that match our results
        where(id_column => compound_ids).
        # Put the compounds into a hash with the id
        # as the key for quick access
        reduce(Hash.new) do |h,c|
          h[c.send(id_column)] = c; h
        end



      # Add the compounds we got from the database
      # to the results
      ms_search.results.each do |r|
        r.name = compounds[r.compound_id].try(name_column)
        r.compound = compounds[r.compound_id]
      end


      return ms_search
    end

    #def self.sources( params, specdb_options={} )

      # set the database param
      #params[:database] = specdb_options[:accessor_database_name] || Specdb.config.accessor_database_name

      # Do the search
      #ms_search = self.find(:all, params: params).first
      

       # Grab the id_column and name_column from the config file
      #id_column   = specdb_options[:compound_id_column]   || Specdb.config.compound_id_column
      # Grab the matching compound ids
      #compound_ids = ms_search.results.map(&:compound_id)

      #comploud_class = specdb_options[:compound_class] || Specdb.config.compound_class


      # Load the matching compounds in one query and
      #compound_match = comploud_class.exported.where(id_column => compound_ids)

      #compound_source = Hash.new()
      #compound_match.each do |compound|
        #compound_source[compound.chemdb_id] = Array.new()
        #compound.registrations.each do |registration|
          #puts registration.name
          #compound_source[compound.chemdb_id].push(registration.name)
        #end 
      #end

      #return compound_source

    #end

    # *Result* contains information about each matching adduct, the search
    # method returns an list of this type of object
    #
    # Here is an example of the attributes for a result object:
    #
    #   "formula"=>"C2H4O3",
    #   "compound_mass"=>"76.016044",
    #   "adduct"=>"2M+Na",
    #   "adduct_type"=>"+",
    #   "adduct_mass"=>"175.021306",
    #   "delta"=>"0.021296",
    #   "compound_id"=>"HMDB00115",
    #   "name"=>"Glycolic acid"
    #
    # You can also get the related compound (with only the id
    # and name column loaded) for example:
    #
    #   result.compound
    #   # => <Metabolite id: "HMDB00115", common_name: "Glycolic acid">
    #
    # NOTE Currently the results get the compound name from the local database
    # rather than from Specdb
    #
    class Result < SpecdbResource
      attr_accessor :compound

      def as_json(*args)
        # Get rid of ms_search in the returning hash
        super(*args)["ms_search"]
      end
    end
  end
end
