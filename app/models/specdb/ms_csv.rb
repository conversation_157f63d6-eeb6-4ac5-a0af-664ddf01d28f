require 'csv'

module Specdb
  class MsCsv
    #HEADER = %w[compound_id compound_name formula compound_mass adduct
      #adduct_type adduct_mass delta]

    HEADER = %w[compound_id compound_name formula monoisotopic_mass adduct
      adduct_type adduct_m/z delta(ppm), ccs_value ]

    def initialize(search_array)
      @search_array =
        search_array.is_a?(Array) ? search_array : [search_array]
    end

    def convert_to_ppm(result)
      delta = result.delta.to_f
      adct_mass = result.adduct_mass.to_f
      ppm = (delta/adct_mass)*1000000
      return ppm.round
    end
    def convert_to_signf(result)
      four_dec_digit = number_with_precision(result, precision: 4).to_f
      if four_dec_digit.to_s.split(".")[1].length < 4
        x = four_dec_digit.to_s+"0"
        return x
      else
        return four_dec_digit
      end
    end

    def build
      puts "HERE INSIDE THE RIGHT ONE"
      CSV.generate do |csv|
        csv << ['query_mass'] + HEA<PERSON><PERSON>
        puts @search_array
        @search_array.each do |search|
          puts " SEARCH #{search}"
          search.results.each do |r|
          
            next if r.name.blank?
            csv << [search.query_mass, r.compound_id, r.name, r.formula,
              r.compound_mass, r.adduct, r.adduct_type, 
              r.adduct_mass, convert_to_ppm(r), r.ccs_value]
            
          end
        end
      end
    end
  end
end
# csv << [search.query_mass, adduct.database_id,
#   c_name, c_kegg_id, adduct.formula, adduct.exact_mass,
#   adduct.adduct, adduct.adduct_type,
#   adduct.adduct_mass, (adduct.ppm).round]
# - search.results.each do |r|
#   - next if r.compound.nil?
#   tr
#     td
#       = link_to r.compound_id, [ main_app, r.compound ],
#           class: "btn"
#     td = r.name
#     td = html_formula(r.formula)
#     td = convert_to_signf(r.compound_mass)
#     td = r.adduct
#     td
#       = convert_to_signf(r.adduct_mass)
#       br
#       = link_to_calculator_details(r.compound, r.formula)
#     td = convert_to_ppm(r)
#     - if r.ccs_value.nil?
#       td = 'N/A'
#     - else
#       td = r.ccs_value
