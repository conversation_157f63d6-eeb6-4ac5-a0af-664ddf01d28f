module Specdb
  class Spectrum < SpecdbResource
    # Class for searching all the spectra

    include Specdb::HasCompound
    # include Specdb::MzMl

    # has_many :peaks, class_name: "Peak", source: :peak do
    #   def exported
    #     where export: true
    #   end
    # end

    DESCENDANTS = [ Specdb::CMs, Specdb::EiMs, Specdb::NmrOneD,
                    Specdb::MsMs, Specdb::NmrTwoD, Specdb::MsIr ].freeze

    # What database is being accessed on the structure resource?
    # Required
    class_attribute  :accessor_database_name

    # Cache the spectrum resources?
    # Defaults to true
    class_attribute  :cache

    # Configure default settings
    self.element_name    = Specdb.config.spectrum_name
    self.collection_name = Specdb.config.spectrum_collection

    # Find all spectrums of any type when called on Spectrum
    # when called on subclasses, find all of that type only
    #
    # The result can be grouped by type like
    #   Spectrum.find_all_by_inchi_key("inchikey").group_by_type
    #   #=> {:nmr1d => [], :ms_ms => []}
    #
    def self.find_all_by_inchi_key(inchi_key)
      threads = []
      spectra = []
      self.descendants.each do |klass|
        threads << Thread.new do
        klass.find :all, :params => {:inchikey => inchi_key}
        end
      end
      threads.each {|t| t.join}
      SpectrumCollection.new spectra.flatten
    end

    def self.find_all_by_database_id(database_id)
      threads = []
      spectra = []
      self.descendants.each do |klass|
        threads << Thread.new do
            begin
              spectra.push(klass.find :all, :params => {:database_id => database_id})
            rescue Exception => e 
              puts e.message
            end
          end
      end
      threads.each {|t| t.join}
      SpectrumCollection.new spectra.flatten
    end

    def self.spectrum_type
      @spectrum_type ||= self.name.split("::").last.underscore
    end

    def spectrum_type
      self.class.spectrum_type
    end

    # By default the element_name of subclasses is set
    # to the underscore name of the class
    def self.inherited(subclass)
      # Set the element name from the subclass name
      subclass_name         = subclass.spectrum_type
      subclass.element_name = subclass_name
    end

    def self.descendants
      DESCENDANTS
    end

    def self.find_all_by_inchi(inchi_key)
      results = self.descendants.maps do |klass|
        klass.where(:inchikey => inchi_key)
      end.flatten
      SpectrumCollection.new results
    end


  end
end
