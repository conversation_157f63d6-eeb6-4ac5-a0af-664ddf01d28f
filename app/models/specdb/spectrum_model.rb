module Specdb
  class SpectrumModel < SpecdbResource
    # By default Spectrums with the default site, password
    # and user. By default Spectrums fetch json and use 
    # the name of the class as the element_name. 

    #
    # For example
    #   
    #   class HNmr < Spectrum; end
    # 
    # will access spectrums at
    #   
    #   http://[site]/h_nmr
    #
    # To override the default setting set them in the class definition
    #   
    #   class NmrOneD < Spectrum
    #     self.element_name = 'nmr_specs'
    #   end
    #
    # will access spectrums at
    #  
    #  http://[site]/nmr_specs
    #
    # Default model for LC-MS
    include Specdb::HasCompound
    include Specdb::MzMl
    # What database is being accessed on the structure resource?
    # Required
    class_attribute  :accessor_database_name

    # Cache the spectrum resources?
    # Defaults to true
    class_attribute  :cache	

    # Configure default setting
    self.element_name    = Specdb.config.spectrum_name
    self.collection_name = Specdb.config.spectrum_collection

    def self.find_all_by_id(id)
      spectra = self.descendants.map do |klass|
        klass.find :all, :params => {:id => id}
      end.flatten
      SpectrumCollection.new spectra
    end

    def self.find_all_by_inchi_key(inchi_key)
      spectra = self.descendants.map do |klass|
        klass.find :all, :params => {:inchikey => inchi_key}
      end.flatten
      SpectrumCollection.new spectra
    end

    def self.spectrum_type
      @spectrum_type ||= self.name.split("::").last.underscore
    end

    def spectrum_type
      self.class.spectrum_type
    end

    # By default the element_name of subclasses is set
    # to the underscore name of the class
    def self.inherited(subclass)
      # Set the element name from the subclass name
      subclass_name         = subclass.spectrum_type 
      subclass.element_name = subclass_name
    end

    # In subclasses of Spectrum, find_all_by_inchi
    # returns a list of spectrums of that type
    def self.find_all_by_inchi_key(inchi_key)
      where(inchi_key: inchi_key)
    end

    # Return the peaks
    def peaks
      Peak.find :all, :params => {
        :spectrum_type => self.class.collection_name,
        :spectrum_id => self.id
      }
    end

    # Return the peak assignments
    def peak_assignments_for(peak_id)
      Assignment.find :all, :params => {
        :spectrum_type => self.class.collection_name,
        :spectrum_id => self.id,
        :peak_id => peak_id
      } 
    end

    def spectrum_image
      # Get either the Spectra image with peak assignments or the
      # raw Spectrum Image. If neither are found then return
      # nil.
      if defined? self.documents
        @spectrum_image ||=
          self.documents.find{|x| /Spectra image with peak assignments/i.match x.description } ||
          # Left both these cases in during merge conflict, not sure which one to keep
          self.documents.find{|x| /spectrum image/i.match x.description } ||
          self.documents.find{|x| /Spectra image/i.match x.description } ||
          "NA"
      else
        @spectrum_image = "NA"
      end
      @spectrum_image == "NA" ? nil : @spectrum_image
    end

    def conditions_present?
      [:sample_concentration, :sample_ph, :sample_temp, :chemical_shift_ref, :solvent,
       :mass, :manufacturer, :instrument_type, :ionization, :frequency, :sample_assessment,
       :spectrum_assessment].reduce(false) do |found,method|
         found || ( self.respond_to?(method) && !self.send(method).blank? )
       end
    end

    def mass
      if self.sample_mass.present?
        mass = self.sample_mass.to_s
        if self.sample_mass_units.present?
          mass = mass + " " + self.sample_mass_units
        end
        return mass
      else
        return nil
      end
    end

    def concentration
      if self.sample_concentration.present?
        concentration = self.sample_concentration.to_s
        if self.sample_concentration_units.present?
          concentration = concentration + " " + self.sample_concentration_units
        end
        return concentration
      else
        return nil
      end
    end

    def temperature
      if self.respond_to?(:sample_temperature) && self.sample_temperature.present?
        temperature = self.sample_temperature.to_s
        if self.sample_temperature_units.present?
          temperature = temperature + " " + self.sample_temperature_units
        end
        return temperature
      else
        return nil
      end
    end


    def binary_mz
      a = self.peaks.map(&:mass_charge).map(&:to_f)
      Base64.encode64(a.pack("E"*self.peaks.count))
    end

    def binary_intensity
      a = self.peaks.map(&:intensity).map(&:to_f)
       Base64.encode64(a.pack("E"*self.peaks.count))
    end

    # Changes a solvent string into a superscripted form
    # @note This function must be expanded as more solvents are added that need subscripting
    # @param [String] solvent A string of the solvent
    # @return [String] The subscripted version of the solvent
    def subscript_solvent(solvent)
      # Map all the solvents that need subscripting to their subscripted versions
      # Add to this map when a new solvent is added that needs subscripting
      subscript_hash = {'C5D5N' => 'C<sub>5</sub>D<sub>5</sub>N'.html_safe,
                        'CD3OD' => 'CD<sub>3</sub>OD'.html_safe,
                        'CD3COOD' => 'CD<sub>3</sub>COOD'.html_safe,
                        'CDCl3' => 'CDCl<sub>3</sub>'.html_safe,
                        'H2O' => 'H<sub>2</sub>O'.html_safe,
                        'CD2Cl2' => 'CD<sub>2</sub>Cl<sub>2</sub>'.html_safe,
                        'CCl3' => 'CCl<sub>3</sub>'.html_safe,
                        'D2O' => 'D<sub>2</sub>O'.html_safe,
                        'CHCL3' => 'CHCL<sub>3</sub>'.html_safe,
                        'C5H5N' => 'C<sub>5</sub>H<sub>5</sub>N'.html_safe,
                        'CD3OH' => 'CD<sub>3</sub>OH'.html_safe,
                        'CHCl3' => 'CHCl<sub>3</sub>'.html_safe,
                        'C6D6' => 'C<sub>6</sub>D<sub>6</sub>'.html_safe,
                        'CH3OD' => 'CH<sub>3</sub>OD'.html_safe,
                        'CD3Cl' => 'CD<sub>3</sub>Cl'.html_safe,
                        'CF3CO2D' => 'CF<sub>3</sub>CO<sub>2</sub>D'.html_safe,
                        'CF3COOD' => 'CF<sub>3</sub>COOD'.html_safe,
                        'CS2' => 'CS<sub>2</sub>'.html_safe,
                        'CCl4' => 'CCl<sub>4</sub>'.html_safe,
                        'CD3CN' => 'CD<sub>3</sub>CN'.html_safe,
                        'C5D6N' => 'C<sub>5</sub>D<sub>6</sub>N'.html_safe,
                        'CF3COOH' => 'CF<sub>3</sub>COOH'.html_safe,
                        'C6H6' => 'C<sub>6</sub>H<sub>6</sub>'.html_safe,
                        'ND3' => 'ND<sub>3</sub>'.html_safe,
                        'Water' => 'H<sub>2</sub>O'.html_safe}

      # Match groups from the hash and replace matching groups with the subscripted version
      re = Regexp.new(subscript_hash.keys.map { |x| Regexp.escape(x) }.join('|'))
      solvent.gsub(re, subscript_hash)
    end

  end
end
