module Specdb


  class Mzcal < ActiveRecord::Base
    include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  # Takes a compound and builds all of the associated adducts
    def self.adduct_for_compound(compound)
      compound_mono_mass = (compound.moldb_mono_mass).to_f 
      adduct_array = Array.new
      self.adduct_groups.each do |type, adducts|
        adducts.each do |adduct, formula|
          adduct_array.push({adduct: adduct, charge: type, mz: formula.call(compound_mono_mass).to_f})
        end
      end
      return adduct_array
    end

    def self.single_positive_adducts
      MASS_TO_SINGLE_POSITIVE_ION_MASS
    end

    def self.double_positive_adducts
      MASS_TO_DOUBLE_POSITIVE_ION_MASS
    end

    def self.triple_positive_adducts
      MASS_TO_TRIPLE_POSITIVE_ION_MASS
    end

    def self.single_negative_adducts
      MASS_TO_SINGLE_NEGATIVE_ION_MASS
    end

    def self.double_negative_adducts
      MASS_TO_DOUBLE_NEGATIVE_ION_MASS
    end

    def self.triple_negative_adducts
      MASS_TO_TRIPLE_NEGATIVE_ION_MASS
    end



    def self.neutral_adducts
      MASS_TO_NEUTRAL_MASS
    end

    def self.adduct_groups
      {
        '1+' => self.single_positive_adducts,
        '2+' => self.double_positive_adducts,
        '3+' => self.triple_positive_adducts,
        '1-' => self.single_negative_adducts,
        '2-' => self.double_negative_adducts,
        '3-' => self.triple_negative_adducts,
      }.freeze
    end


  # Define the methods to allow for calculating the ion mass
  # given the ion type and mass of the compound
    MASS_TO_SINGLE_POSITIVE_ION_MASS = {
      "M+H"            => lambda { |mass| mass + 1.007276   },
      "M-2H2O+H"       => lambda { |mass| mass - 35.0127  },
      "M-H2O+H"        => lambda { |mass| mass - 17.0027  },
      "M-H2O+NH4"      => lambda { |mass| mass + 0.0227   },
      "M+Li"           => lambda { |mass| mass + 7.0160   },
      "M+NH4"          => lambda { |mass| mass + 18.033823   },
      "M+Na"           => lambda { |mass| mass + 22.989218   },
      "M+CH3OH+H"      => lambda { |mass| mass + 33.033489   },
      "M+K"            => lambda { |mass| mass + 38.963158   },
      "M+ACN+H"        => lambda { |mass| mass + 42.033823  },
      "M+2Na-H"        => lambda { |mass| mass + 44.971160   },
      "M+IsoProp+H"        => lambda { |mass| mass + 61.06534  },
      "M+ACN+Na"       => lambda { |mass| mass + 64.015765   },
      "M+2K-H"         => lambda { |mass| mass + 76.919040   },
      "M+DMSO+H"       => lambda { |mass| mass + 79.02122    },
      "M+2ACN+H"       => lambda { |mass| mass + 83.060370  },
      "M+IsoProp+Na+H"     => lambda { |mass| mass + 84.0551   },
      "2M+H"           => lambda { |mass| 2*mass + 1.007276  },
      "2M+NH4"         => lambda { |mass| 2*mass + 18.033823 },
      "2M+Na"          => lambda { |mass| 2*mass + 22.989218 },
      "2M+3H2O+2H"     => lambda { |mass| 2*mass + 28.02312  },
      "2M+K"           => lambda { |mass| 2*mass + 38.963158 },
      "2M+ACN+H"       => lambda { |mass| 2*mass + 42.033823 },
      "2M+ACN+Na"      => lambda { |mass| 2*mass + 64.015765 }
    }.freeze

    MASS_TO_DOUBLE_POSITIVE_ION_MASS = {
      "M+2H"           => lambda { |mass| mass/2 + 1.007276  },
      "M+H+NH4"        => lambda { |mass| mass/2 + 9.520550 },
      "M+H+Na"         => lambda { |mass| mass/2 + 11.998247 },
      "M+H+K"          => lambda { |mass| mass/2 + 19.985217 },
      "M+ACN+2H"       => lambda { |mass| mass/2 + 21.520550 },
      "M+2Na"          => lambda { |mass| mass/2 + 22.989218 },
      "M+2ACN+2H"      => lambda { |mass| mass/2 + 42.033823 },
      "M+3ACN+2H"      => lambda { |mass| mass/2 + 62.547097 }
    }.freeze

   
  MASS_TO_TRIPLE_POSITIVE_ION_MASS = {
    "M+3H"      => lambda { |mass| mass/3 + 1.007276 },
    "M+Na+2H"   => lambda { |mass| mass/3 + 8.3346},
    "M+2Na+H"   => lambda { |mass| mass/3 + 15.7662   },
    "M+3Na"     => lambda { |mass| mass/3 + 22.989218 }
  }.freeze

   

    MASS_TO_SINGLE_NEGATIVE_ION_MASS = {
      "M-H"            => lambda { |mass| mass - 1.007276    },
      "M-H2O-H"        => lambda { |mass| mass - 19.01839    },
      "M+F"            => lambda { |mass| mass + 18.9984    },
      "M+Na-2H"        => lambda { |mass| mass + 20.974666   },
      "M+Cl"           => lambda { |mass| mass + 34.969402   },
      "M+K-2H"         => lambda { |mass| mass + 36.948606   },
      "M+FA-H"         => lambda { |mass| mass + 44.998201   },
      "M+HAc-H"        => lambda { |mass| mass + 59.013851   },
      "M+Br"           => lambda { |mass| mass + 78.918885   },
      "M+TFA-H"        => lambda { |mass| mass + 112.985586  },
      "2M-H"           => lambda { |mass| 2*mass - 1.007276  },
      "2M+FA-H"        => lambda { |mass| 2*mass + 44.998201 },
      "2M+HAc-H"       => lambda { |mass| 2*mass + 59.013851 },
      "3M-H"           => lambda { |mass| 3*mass - 1.007276  }
    }.freeze


    MASS_TO_DOUBLE_NEGATIVE_ION_MASS = {
      "M-2H"           => lambda { |mass| mass/2 - 1.007276  }
    }.freeze


    MASS_TO_TRIPLE_NEGATIVE_ION_MASS = {
      "M-3H"           => lambda { |mass| mass/3 - 1.007276  }
      }.freeze


  


    

 
  end
end





