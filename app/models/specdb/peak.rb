module Specdb
  class Peak < SpecdbResource
    # Allow to use this peak model for peaks from
    # any type of spectrum
    self.prefix = "/:spectrum_type/:spectrum_id/"

    def assignments
      if self.attributes.has_key? "assignments"
        return self.attributes[:assignments]
      else
        # Return an empty array instead of nil
        Array.new
      end
    end

    class Assignment < SpecdbResource
      # Allow to use this assignment model for peaks from
      # any type of spectrum
      self.prefix = "/:spectrum_type/:spectrum_id/peaks/:peak_id/"
    end
  end
end
