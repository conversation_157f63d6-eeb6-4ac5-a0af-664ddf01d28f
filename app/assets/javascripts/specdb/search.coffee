peakListData = null

$ ->
  # Show loading gif when clicking on specific links, only if we are NOT holding down a key
  # (i.e. command-click opening a new window)
  $('.show-loader').click (e) ->
    if !e.metaKey
      $('#load-screen').show()

  # Make sure we trun off the loading gif when you use back links
  $(document).on "page:change", ->
    $('#load-screen').hide()

initialize_loader_ri = ->
  $('.example-loader-ri').click (evt) ->
    if $('#retention-fields').length >= 0
      $('#retention-fields').show()
    # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Fill in the form using the JSON object
    for field, value of data
      form.find("##{field}").val(value)

    evt.preventDefault()

initialize_loader_ms_ms_ccs = ->
  $('.example-loader-ms_ms_ccs').click (evt) ->
    # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Fill in the form using the JSON object
    for field, value of data
      form.find("##{field}").val(value)

    evt.preventDefault()

initialize_loader_ms_ms = ->
  $('.example-loader-ms_ms').click (evt) ->
    # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Fill in the form using the JSON object
    for field, value of data
      form.find("##{field}").val(value)

    evt.preventDefault()

initialize_loader_pl =  ->
  $('.example-loader-pl').click (evt) ->
    $('#retention-fields').hide()
    # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Fill in the form using the JSON object
    for field, value of data
      form.find("##{field}").val(value)

    evt.preventDefault()

initialize_loader_ms_ccs = ->
  $('.example-loader-ms-ccs').click (evt) ->
    # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Fill in the form using the JSON object
    for field, value of data
      form.find("##{field}").val(value)

    evt.preventDefault()


initialize_loader_ms = ->
  $('.example-loader-ms').click (evt) ->
    # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Fill in the form using the JSON object
    for field, value of data
      form.find("##{field}").val(value)

    evt.preventDefault()

initialize_nmr_search_loaders = ->
  $('.example-loader-nmr-search').click (evt) ->
  # Get the form for this example
    form = $(evt.currentTarget).closest('.specdb-search-form')
    search_type = form.find('#search_type').val()
    nucleus = form.find('#nucleus').val()
    # Read the data from the example button as JSON
    data = JSON.parse($(evt.currentTarget).data('example').replace(/'/g,""))
    # Remove error texts, if any
    $('#peaks').removeClass('final')
    # Fill in the form using the JSON object
    for field, value of data[search_type][nucleus]
      form.find("##{field}").val(value)

    evt.preventDefault()

on_ready ->
  initialize_nmr_search_loaders()
  initialize_loader_ms()
  initialize_loader_ms_ccs()
  initialize_loader_pl()
  initialize_loader_ms_ms()
  initialize_loader_ms_ms_ccs()
  initialize_loader_ri()

on_ready ->
  if $('#reset').length == 0
    return
  $('#reset').click (evt) ->
    $('#retention-fields').hide()

on_ready ->
  if $('.search-tabs').length
    $('a[href="'+location.hash+'"]').tab('show')

window.retrieving = false

on_ready ->
  $('.results-table a.compare').click loadCandidate
  if window.compareCandidate?
    $("#comparison-compound").html(window.compareCandidate.compound_name + " (" + window.compareCandidate.compound_id + ")")

on_ready ->  
  $('#nmr-one-mirror').hide()
  $('.results-table-one-d a.compare').click loadCandidateOne
  if window.compareCandidate?
    $("#comparison-compound").html(window.compareCandidate.compound_name + " (" + window.compareCandidate.compound_id + ")")

# Uses ajax to grab a candidate spectra data and then display it
loadCandidate = (evt) ->
  id = $(this).attr("data-spectrum_id")
  spec_name = $(this).attr("spec_name")
  if !window.retrieving
    $(".spectrum-overlay").show()
    window.retrieving = true
    $.ajax
      url: "/spectra/#{spec_name}/load_candidate"
      type: "GET"
      dataType: "JSON"
      data: {'id': id}
      success: (result) ->
        # If the status is complete we can display it!
        $("[data-spectrum_id=" + window.compareCandidate.spectrum_id + "]").
          removeClass("btn-current").
          addClass("btn-compare").
          removeAttr("disabled").
          html("Compare Spectrum")
        $("[data-spectrum_id=" + result.spectrum_id + "]").
          removeClass("btn-compare").
          addClass("btn-current").
          attr("disabled", "disabled").
          html("Current Spectrum")
        $("#comparison-compound").html(result.compound_name + " (" + result.compound_id + ")")
        window.compareCandidate = result

        window.retrieving = false
        window.dispatchEvent(new CustomEvent("compare"))
        $(".spectrum-overlay").fadeOut()

      error: ->
        alert('Error occurred.')
        window.retrieving = false
        $(".spectrum-overlay").fadeOut()

# Uses ajax to grab a candidate spectra data and then display it
loadCandidateOne = (evt) ->
  id = $(this).attr("data-spectrum_id")
  spec_name = $(this).attr("spec_name")
  if !window.retrieving
    $(".spectrum-overlay").show()
    window.retrieving = true
    $.ajax
      url: "/spectra/#{spec_name}/load_candidate"
      type: "GET"
      dataType: "JSON"
      data: {'id': id}
      success: (result) ->
        $('#nmr-one-mirror').show()
        # If the status is complete we can display it!
        current_result = $("#comparison-compound").val()
        if current_result == ""
          current_result = result.spectrum_id
        $("#comparison-compound").html(result.compound_name + " (" + result.compound_id + ")")
        window.compareCandidate = result
        $("[data-spectrum_id=" + current_result + "]").
          removeClass("btn-current").
          addClass("btn-compare").
          removeAttr("disabled").
          html("Compare Spectrum")
        $("[data-spectrum_id=" + result.spectrum_id + "]").
          removeClass("btn-compare").
          addClass("btn-current").
          attr("disabled", "disabled").
          html("Current Spectrum")
        $("#comparison-compound").val(result.spectrum_id)
        
        window.compareCandidate = result
        window.retrieving = false
        window.dispatchEvent(new CustomEvent("compare"))
        $(".spectrum-overlay").fadeOut()
        

      error: ->
        alert('Error occurred.')
        window.retrieving = false
        $(".spectrum-overlay").fadeOut()

$ ->
  if $('#retention-fields').length == 0
    return
  $('#retention').on 'input', (e) ->
    if ($('#retention').val().length > 0)
      $('#retention-fields').show()
    else
      $('#retention-fields').hide()
    return

on_ready ->
  minPeaks = 1
  maxPeaks = 3000
  # This is based on the number of peaks moldb will have to load. The higher the number this is, the slower the
  # queries will be do to fetch time
  maxMargin = 500000
  # Handle Jquery UI Slider
  slider = $('#range-slider').slider
    range: true
    min: minPeaks
    max: maxPeaks
    values: [
      1
      10
    ]
    slide: (event, ui) ->
# Change the input box when user uses slider
      $('#range-start').val(ui.values[0])
      $('#range-end').val(ui.values[1])
      # Update textboxes / warnings
      verifyMargin(ui.values[0], ui.values[1], maxMargin)
      return

  $("#range-start").val(slider.slider("values")[0])
  $("#range-end").val(slider.slider("values")[1])

  # Change slider when user inputs in the textbox
  $("#range-start").on 'input', ->
    slider.slider("values", 0, $(this).val())
    verifyRangeMinMax($(this), minPeaks, maxPeaks)
    verifyMargin($("#range-start").val(), $("#range-end").val(), maxMargin)
  $("#range-end").on 'input', ->
    slider.slider("values", 1, $(this).val())
    verifyRangeMinMax($(this), minPeaks, maxPeaks)
    verifyMargin($("#range-start").val(), $("#range-end").val(), maxMargin)

# Linking the sliders with the text boxes
on_ready ->
  linkSliders('#percent-slider', '#percent-value',
    parseInt($('#percent-value').attr('max')), parseInt($('#percent-value').attr('min')))
  linkSliders('#exact-slider', '#exact-value',
    parseInt($('#exact-value').attr('max')), parseInt($('#exact-value').attr('min')))

on_ready ->
# Handle when user selects to search for over 3000 peaks
  $('#num_peaks_range_over').change ->
    if($(this).is(':checked'))
      $('#range-start').prop("disabled", true)
      $('#range-end').prop("disabled", true)
      $('#range-slider').slider('disable')
      $('#nmr-search-button').prop("disabled", false)
    else
      $('#range-start').prop("disabled", false)
      $('#range-end').prop("disabled", false)
      $('#range-slider').slider('enable')
      if ($('#range-info').hasClass('alert-danger'))
        $('#nmr-search-button').prop("disabled", true)

on_ready ->
# Clear error if there is one when user starts inputting into the chemical shift input textarea
  $('#peaks').on 'input', ->
    $(this).removeClass('final')
    $('#chemical-shift-error').html("")

###
  * Adds disabled and hides all range advanced search options
 ###
disableRange = ->
  $('#range-search').hide()
  $('#range-start').prop("disabled", true)
  $('#range-end').prop("disabled", true)

###
  * Adds disabled and hides all percent advanced search options
 ###
disablePercent = ->
  $('#percent-search').hide()
  $('#percent-value').prop("disabled", true)
  $('#percent-slider').prop("disabled", true)

###
  * Adds disabled and hides all exact advanced search options
 ###
disableExact = ->
  $('#exact-search').hide()
  $('#exact-value').prop("disabled", true)
  $('#exact-slider').prop("disabled", true)

###
  * Changes the warnings based on the number of potential results. Will turn green if query should be fast, yellow
  * if slow and red as invalid
  * @param {Number} warningType 0 for red / invalid, 1 for yellow / slow and 2 for green / fast
 ###
changeRangeWarnings = (warningType) ->
  switch warningType
    when 0
      changeAlertClasses($('#range-info'), true, 'alert-danger')
      $('div.ui-slider-range.ui-widget-header').css("background", "red")
      $('#nmr-search-button').prop("disabled", true)
      changeRangeTextClasses($('#range-start'), true, 'invalid-input')
      changeRangeTextClasses($('#range-end'), true, 'invalid-input')
      addErrorMessage("Margin is too large")
    when 1
      changeAlertClasses($('#range-info'), true, 'alert-warning')
      $('div.ui-slider-range.ui-widget-header').css("background", "yellow")
      $('#nmr-search-button').prop("disabled", false)
      changeRangeTextClasses($('#range-start'), true, 'warning-input')
      changeRangeTextClasses($('#range-end'), true, 'warning-input')
      removeErrorMessage()
    when 2
      changeAlertClasses($('#range-info'), true, 'alert-success')
      $('div.ui-slider-range.ui-widget-header').css("background", "green")
      $('#nmr-search-button').prop("disabled", false)
      changeRangeTextClasses($('#range-start'), false)
      changeRangeTextClasses($('#range-end'), false)
      removeErrorMessage()

###
  * Changes the input class and adds one if specified
  * @param {Object} elem the jQuery selected element to remove / add classes on
  * @param {Boolean} willAddClass if we are adding a class or not
  * @param {String} newClass the new class to be added
 ###
changeRangeTextClasses = (elem, willAddClass, newClass="") ->
  elem.removeClass("warning-input")
  elem.removeClass("invalid-input")
  if !willAddClass then return
  elem.addClass(newClass)

###
  * Changes the alert class on the alert box and adds one if specified
  * @param {Object} elem the jQuery selected element to remove / add classes on
  * @param {Boolean} willAddClass if we are adding a class or not
  * @param {String} newClass the new class to be added
 ###
changeAlertClasses = (elem, willAddClass, newClass="") ->
  elem.removeClass("alert-danger")
  elem.removeClass("alert-warning")
  elem.removeClass("alert-success")
  if !willAddClass then return
  elem.addClass(newClass)

###
  * Checks if the margin between the two sliders is too large
  * Margin is determined by checking how many records moldb will
  * have to load to perform the search
  * @param {Number} start the value of the first slider
  * @param {Number} end the value of the second slider
  * @param {Number} max the max distance between the two sliders
 ###
verifyMargin = (start, end, max) ->
  loadPeakList()
  diff = end - start
  totalPeaks = 0
  # Don't want to calculate if user inputs a number large than the max
  if (diff < 3000)
    for peakCounter in [start..end]
      if peakListData.hasOwnProperty(peakCounter.toString())
        totalPeaks += peakListData[peakCounter.toString()]
    if (totalPeaks > max || diff < 0)
      changeRangeWarnings(0)
    else if (totalPeaks > max / 2)
      changeRangeWarnings(1)
    else
      changeRangeWarnings(2)
  else
    changeRangeWarnings(2)

###
  * Checks if the input is larger than or smaller than the min / max
  * @param {Object} the jQuery selected object
  * @param {Number} min the min acceptable value
  * @param {Number} max the max acceptable value
 ###
verifyRangeMinMax = (elem, min, max) ->
  if (elem.val() > max) || (elem.val() < min)
    elem.addClass('invalid-input')
    addErrorMessage('Invalid Number of Peaks')
  else
    elem.removeClass('invalid-input')
    removeErrorMessage()

###
  * Adds an error message to the invisble div and displays it
  * @param {String} message the message to display
 ###
addErrorMessage = (message) ->
  $('#error-ctn').text(message)
  $('#error-ctn').removeClass('invisible')
  $('#error-ctn').addClass('visible')

###
  * Removes the error message and sets it to invisble
 ###
removeErrorMessage = ->
  $('#error-ctn').text()
  $('#error-ctn').removeClass('visible')
  $('#error-ctn').addClass('invisible')

###
 * Loads the peak list
 ###
loadPeakList = ->
  if peakListData == null
    peakListData = JSON.parse($('#peak-data').data('peak-lists').replace(/'/g,""))


###
  * Links the slider with the input text box and adds on input listeners to the textbox
  * @param {String} slider the selector for the slider
  * @param {String} value the selector for the input textbox
  * @param {Number} min the min acceptable value
  * @param {Number} max the max acceptable value
 ###
linkSliders = (slider, value, max, min) ->
  $(slider).on 'input', ->
    $(value).val($(this).val())
    #  Uncomment this to automatically change input to min/max when user is under/over
    #  $(value).change ->
    #    if ($(this).val() < min)
    #      $(this).val(min)
    #    else if ($(this).val() > max)
    #      $(this).val(max)
    #    else if (!$(this).val())
    #      $(this).val(20)
    $(value).removeClass("invalid-input")
    $('#nmr-search-button').prop("disabled", false)
    $(slider).val($(this).val())

  $(value).on 'input', ->
    $(slider).val($(this).val())
    if ($(this).val() < min || $(this).val() > max || !$(this).val())
      $(this).addClass("invalid-input")
      $('#nmr-search-button').prop("disabled", true)
      addSlidersErrors($(this).val(), min, max)
    else
      $(this).removeClass("invalid-input")
      removeErrorMessage()
      $('#nmr-search-button').prop("disabled", false)

###
  * Adds an error message in the invisble div
  * @param {Number} value the current value
  * @param {Number} min the min acceptable value
  * @param {Number} max the max acceptable value
 ###
addSlidersErrors = (value, min, max) ->
  if (!value)
    addErrorMessage('Input must not be blank!')
  else if (value < min )
    addErrorMessage('Input is too small!')
  else if (value > max)
    addErrorMessage('Input is too large!')


# $ ->
#   if $('#nmr-right-form').length && $('#search_type').length
#     $('#search_type').change ->
#       if this.value == 'mixture'
#         $('#peaks').attr('placeholder',  'Enter one peak position per line')
#         $('#nmr-search-freq').val("") # clear any input to not affect the search
#         $('#frequency-form-group').hide()
#       else if this.value = 'pure_compound'
#         $('#peaks').attr('placeholder', 'Enter one peak position and one peak intensity value (space seperated) per line')
#         $('#frequency-form-group').show()

