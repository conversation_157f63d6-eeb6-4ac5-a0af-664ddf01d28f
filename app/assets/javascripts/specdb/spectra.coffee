class @SpectrumChart
  x_title: "X title"
  y_title: "Y title"
  x_getter: (d) -> +d.x
  y_getter: (d) -> +d.y
  shift_intensity_x:  1
  shift_intensity_y:  1

  # Returns an array of Chart objects
  @buildAll: (select_string, options={}) ->
    klass = this
    d3.selectAll(select_string).map (temp) -> new klass(d3.select(this))

  # Override this to change the set up for the sidebar
  setup_chart_info: ->
    #@chart_info.append("h2").text("Peak Assignments")
    #@peak_assignment_list = @chart_info.append("div")
    # .attr("class","peak-assignment-list")
    #@no_peaks_message = @chart_info.append("p")
    # .text("No assignments found for this peak.")
    # .style("display","none")

  # Draws the peak assignments, should be called when a peak is
  # selected.
  draw_peak_assignments: (peak) =>
    @chart.selectAll("div.peak-assignment")
      .attr("class","peak-assignment hidden")

    if peak.assignment
      @chart.select("#assignment-"+peak.assignment.id)
        .attr("class","peak-assignment")


  peak_url: (peak_path) ->
    peak_path

  # Override these "orient_" methods to change the orientation of
  # the graph.

  # Orients the canvas
  orient_canvas: (canvas) =>
    canvas.attr("transform", "translate(" + (@width + @margin.left) + "," + ( @height + @margin.top ) + ") scale(-1,-1)")

  #
  orient_x_rules: (x_rules) =>
    x_rules.attr("transform","scale(1,-1)")

  orient_x_rule_numbers: (x_rule_numbers) =>
    x_rule_numbers.attr("transform", "scale(-1,1)")

  orient_intensity: (intensity) =>
    intensity.attr("transform","scale(" + ( -@shift_intensity_x) + "," + ( -@shift_intensity_y ) + ")")

  constructor: (@chart) ->
    @width  = 500
    @height = 300
    @zoom_window_height = 80
    @margin =
      top:    10
      right:  10
      bottom: 60
      left:   60

    @setup_chart()
    @setup_chart_info()

    # Grab the data-url from the spectrum-chart div
    @data_url = @chart.attr("data-url")

    # Fetch the json data from the data-url and when it is
    # downloaded, draw the chart.
    @fetch_data @data_url, @draw_chart


  fetch_data: (data_url, draw_callback) ->
    # Set up some local variables so we can access them
    # in the d3 fetcher context
    drawer     = this
    x_getter   = @x_getter
    y_getter   = @y_getter
    sort_peaks = @sort_peaks
    normalize_intensity = @normalize_intensity

    # Fetch the peak/assignment data and draw the chart.
    d3.json @peaks_data_url(), (data) ->
      # Convert strings to numbers.
      data.forEach (peak,i) ->
        # Set up some extra attributes on the peaks
        peak.x          = +x_getter(peak)
        peak.y          = +y_getter(peak)
        peak.selected   = false
        peak.assignment = if peak.assignments.length > 0 then peak.assignments[0] else null

      # Sort the peaks after setting up the x,y attributes
      # so that we can use the x,y attibutes in the sort
      # function
      peaks = sort_peaks(data)
      peaks.forEach (peak,i) ->
        # Add index number
        peak.index = i

      drawer.peaks = data
      draw_callback.call(drawer)

  # Sort peaks by x-axis
  sort_peaks: ( peaks ) ->
    peaks.sort (a,b) ->
      a.x - b.x


  # Appends the peaks data location onto the base data-url
  peaks_data_url: ->
    @data_url + "/peaks.json"

  # Sets up the container elements for the chart
  setup_chart: ->
    @chart_title = @chart.attr("data-title")
    # TODO remove the javascript require message!!
    @specdiv = @chart.append("div")
      .attr("class","spectrum")
    @specdiv.append("h1").text(@chart_title)

    @specsvg = @specdiv
      .append("svg")
      .style("padding-left", "30px")
      .attr("width",  @width  + @margin.left + @margin.right)
      .attr("height", @height + @margin.top  + @margin.bottom)

    @specdiv
      .append("p")
      .attr("class","instructions")
      .text("Hover over peaks or use controls on the right hand side to view the assignments for each peak.")

    @y_rules_container = @specsvg.append("g")

    # Draw the peaks here
    @canvas = @specsvg
      .append("g")
      .call(@orient_canvas)

    # Add a container for the toolbar
    @append_toolbar()

    # Add a container for the chart info
    #@append_
    #@append_chart_info()

  # Draws the div for the chart information
  #append_chart_info: ->
  # # A container for info about the spectrum
  # @chart_info = @chart.append("div")
  #   .attr("class","chart-info")

  # Draws the toolbar
  append_toolbar: ->
    @char_toolbar = @chart.append("div")
      .attr("class","toolbar")

    @next_button = @char_toolbar.append("div")
      .attr("class","btn next")
      .on("click", @select_next_peak )

    @prev_button = @char_toolbar.append("div")
      .attr("class","btn prev")
      .on("click", @select_prev_peak )

    @char_toolbar.selectAll("div.btn")
      .on("mousedown", @draw_pressed_button)
      .on("mouseup",   @draw_released_button)
      .on("mouseout",  @draw_released_button)


  select_next_peak: =>
    i = @selected_peak || 0
    @select_peak i+1

  select_prev_peak: =>
    i = @selected_peak || 0
    @select_peak i-1

  select_peak: (i) ->
    length = @spectrum_peaks.length
    @selected_peak =
      if  i < 0
         length - 1 - ( (length - 1 - i) % length )
      else
        i % @spectrum_peaks.length

    @spectrum_peaks_container.selectAll(".peak")
      .classed("selected",false)

    # grab the peak from the list of peaks
    peak = d3.select @spectrum_peaks[ @selected_peak ]

    # select this peak
    peak.attr("class","peak selected")
    @draw_peak_assignments( peak.datum() )


  draw_pressed_button: ->
    d3.select(this).classed("pressed",true)

  draw_released_button: ->
    d3.select(this).classed("pressed",false)

  draw_chart: (data) ->
    x =
      max: d3.max( @peaks, (d) -> d.x )
      min: d3.min( @peaks, (d) -> d.x )

    x.scale = d3.scale.linear()
      .range([0, @width])
      .domain([x.min,x.max])
      .nice()

    y =
      max: d3.max( @peaks, (d) -> d.y )
      min: 0

    y.scale = d3.scale.linear()
      .range([0, @height - 40])
      .domain([y.min, y.max])
      .nice()

    # Always include 0 on the chemical shift scale
    x.min = if x.min < 0.0 then x.min else 0.0

    @draw_y_axis(y)
    @draw_x_axis(x)
    @draw_peaks(@peaks,x,y)
    @draw_peak_x_labels(@peaks,x,y)

    # select the first peak
    @select_peak(0)

  draw_peaks: (data,x,y) ->
    # A container to hold the spectrum peaks.
    @spectrum_peaks_container = @canvas.append("g")

    # Need to pass the drawer to the mouseover so we can
    # let the mouseover be bound to the svg element
    drawer = this

    # Draw a line for each peak
    @spectrum_peaks_container.selectAll(".peak").data(data)
      .enter()
      .append("line")
      .attr("class","peak")
      .attr("y1", y.min)
      .attr("y2", (d) => y.scale( d.y ) )
      .attr("x1", (d) => x.scale( d.x ) )
      .attr("x2", (d) => x.scale( d.x ) )
      .on("mouseover", (d,i) -> drawer.select_peak(i) )

    @spectrum_peaks = @spectrum_peaks_container.selectAll(".peak")[0]

  draw_peak_x_labels: (data,x,y) ->
    @peak_x_labels = @canvas.append("g")

    # Need to pass the drawer to the mouseover so we can
    # let the mouseover be bound to the svg element
    drawer = this

    # shift the text so it is not right on top of the peak
    shift_up   =  5
    shift_left = 10

    @peak_x_labels.selectAll("g.peak-x-label").data(data)
      .enter()
      .append("g")
      .attr("class","peak-x-label")
      .attr("transform", (d) => "translate(" + (x.scale(d.x) + @shift_intensity_x * shift_left ) + "," + (y.scale(d.y) + @shift_intensity_y * shift_up)+ ")")
      .append("text")
      .text( (d) => d.x.toFixed(1) )
      .call( @orient_intensity )

  draw_x_axis: (x) ->
    # Create an axis generator
    x_axis_generator = d3.svg.axis()
      .orient("bottom")
      .scale(x.scale)

    # A container to hold the x-axis rules
    @x_rules = @canvas.append("g")
      .attr("class", "axis")
      .call(@orient_x_rules)
      .call(x_axis_generator)

    # unmirror the title labels
    @x_rules.selectAll("text")
      .call(@orient_x_rule_numbers)

    # add a title for the x-axis
    x_title = if @x_title_getter? then @x_title_getter() else @x_title
    @specsvg.append("text")
      .attr("class","title")
      .attr("x",  @width/2 + @margin.left)
      .attr("dy", @height  + @margin.top + @margin.bottom/2 + 10)
      .attr("text-anchor", "middle")
      .text(x_title)


  draw_y_axis: (y) ->
    # A container to hold the y-axis rules
    @y_rules_container
      .attr("transform", "translate("+@margin.left+","+(@<EMAIL>)+") scale(1,-1)" )

    # Add rules to show the intensity values.
    # add a group for the rules
    @y_rules = @y_rules_container.selectAll(".rule")
      .data( y.scale.ticks(10) )
      .enter().append("g")
      .attr("class", "rule")
      .attr("transform", (d) -> "translate(0," + y.scale(d) + ")")

    # add horizontal rule lines
    @y_rules.append("line")
      .attr("x2",@width)

    # add numbering on the left
    @y_rules.append("text")
      .attr("x",-6)
      .attr("dy",".35em")
      .attr("text-anchor", "end")
      .text( (d) -> d )
      .attr("transform","scale(1,-1)")

    # Add a title for the y axis
    y_title = if @y_title_getter? then @y_title_getter() else @y_title
    @specsvg.append("text")
      .attr("class","title")
      .attr("transform","rotate(-90)")
      .attr("y",@margin.left/2 - 10)   # actually the x on the graph because of rotation
      .attr("x", -@height/2 - @margin.top ) # actually the y on the graph because of rotation
      .attr("text-anchor", "middle")
      .text(y_title)


class @MsMsChart extends @SpectrumChart
  x_title: "Mass Charge (m/z)"
  y_title: "Intensity"
  x_getter: (d) -> +d.mass_charge
  y_getter: (d) -> +d.intensity
  shift_intensity_x: -1
  shift_intensity_y:  1

  orient_canvas: (canvas) =>
    canvas.attr("transform", "translate(" + (@margin.left) + "," + ( @height + @margin.top ) + ") scale(1,-1)")

  orient_x_rules: (x_rules) =>
    x_rules.attr("transform","scale(1,-1)")

  orient_x_rule_numbers: (x_rule_numbers) =>

class @CMsChart extends @MsMsChart
  # TODO are gc_ms peaks not supposed to have an intensity ???
  y_getter: (d) ->
    if d.intensity == null then 1.0 else +d.intensity

class @NmrOneDChart extends @SpectrumChart
  x_title: "Chemical Shift"
  y_title: "Intensity"
  x_getter: (d) -> +d.chemical_shift
  y_getter: (d) -> +d.intensity

  orient_intensity: (intensity) =>
    intensity.attr("transform","scale(-1,-1)")

  draw_peak_assignments: (peak) ->
    # Temporarily disable drawing peak assignments for
    # NMR1D

  draw_peak_x_labels: (data,x,y) ->
    # Temporarily disable drawing peak labels for
    # NMR1D

  # Sort peaks by x-axis
  sort_peaks: ( peaks ) ->
    peaks.sort (a,b) ->
      b.x - a.x

class @NmrTwoDChart extends @SpectrumChart
  x_title_getter: -> @chart.attr("data-nucleus-x") + " Chemical Shift (ppm)"
  y_title_getter: -> @chart.attr("data-nucleus-y") + " Chemical Shift (ppm)"
  x_getter: (d) -> +d.chemical_shift_x
  y_getter: (d) -> +d.chemical_shift_y

  # Sort peaks by x-axis
  sort_peaks: ( peaks ) ->
    peaks.sort (a,b) ->
      b.x - a.x

  draw_peaks: (data,x,y) ->
    # A container to hold the spectrum peaks.
    @spectrum_peaks_container = @canvas.append("g")

    # need to pass the drawer to the mouseover so we can
    # let the mouseover be bound to the svg element
    drawer = this

    # Draw a line for each peak
    @spectrum_peaks_container.selectAll(".peak")
      .data(data).enter().append("circle")
      .attr("class","peak")
      .attr("r", 10)
      .attr("cy", (d) => y.scale( d.y ) )
      .attr("cx", (d) => x.scale( d.x ) )
      .on("mouseover", (d,i) -> drawer.select_peak(i) )

    @spectrum_peaks = @spectrum_peaks_container.selectAll(".peak")[0]



# Build the charts in the document
$(document).ready ->
  #NmrOneDChart.buildAll "div.spectrum-chart.nmr_one_d"
  #MsMsChart.buildAll "div.spectrum-chart.ms_ms"
  #NmrTwoDChart.buildAll "div.spectrum-chart.nmr_two_d"
  # Currently there is no good CMs data (no intensities) so
  # we will just display the peak list
  #MsMsChart.buildAll "div.spectrum-chart.c_ms"

  # Tabify the interface
  # $("#spectrum-view-tabs").tabs()

