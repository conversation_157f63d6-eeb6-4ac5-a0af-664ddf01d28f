on_ready ->
  if $('#nmr-spectrum').length
    path = d3.select('#nmr-spectrum').attr('data-path')
    nucleus = d3.select('#nmr-spectrum').attr('data-nucleus')
    name = d3.select('#nmr-spectrum').attr('data-name')
    data_type = d3.select('#nmr-spectrum').attr('data-type')
    source = d3.select('#nmr-spectrum').attr('data-source')
    x_max = if nucleus == '13C' then 205 else 11.1
    showH = nucleus == '1H'

    if data_type == 'json'
      sv = new JSV.SpectraViewer '#nmr-spectrum', {
        debug: false,
        width: $('.panel#spectrum .panel-body').width(),
        min_boundaries: { x:[-1, x_max], y: [-0.02, 1.18] },
        axis_y_lock: 0.04,
        legend_show: false,
        x_axis_label: 'ppm'
      }
      sv.flash('Loading...')
      window.sv = sv

      $.getJSON path, (data) ->
        sv.add_spectrum({
          id: 'sum_line',
          name: name,
          peak_list: data.peaks
        })
        sv.spectra('sum_line').labels.update_peaks()
        sv.draw()

    else if data_type == 'nmrml'

      sv = new JSV.SpectraViewer '#nmr-spectrum', {
        width: $('.panel#spectrum .panel-body').width(),
        drag_drop_load: false,
        axis_y_lock: 0.04,
        axis_x_title: 'ppm',
        legend_show: true,
        cluster_navigation_id: '#cluster-navigation',
        simplify_tolerance: 0.01,
        zoom_max: 1000,
        min_boundaries: { x:[-1, x_max], y: [-0.02, 1.18] }
        highlight: {
          restriction_spectrum_id: 'Assignment',
          element_type: 'peak',
          display: { fill: 'rgba(238, 238, 255, 0.7)' },
          visible_only: false
        },
        select: {
          element_type: 'peak',
          allow_multiselect: true,
          allow_adjust: false,
          allow_peak_creation: false,
          show_indicators: true,
          display: { fill: 'rgba(208, 208, 255, 0.75)' }
        },
        structure: {
          container_id: '#structure',
          use_jsmol: true,
          show_atom_numbers: true,
          show_hydrogen: showH,
          width: 400,
          height: 400
        },
        assignment_table: {
          container_id: '#assignments',
          edit_mode: false
        }
      }
      sv.flash('Loading...')
      window.sv = sv

      $.ajax
        dataType: 'text',
        url: path,
        success: (nmrml) ->
          if nmrml
            if source
              sv.add_nmrml_data(nmrml, source)
            else
              sv.add_nmrml_data(nmrml)
            $('.jsv-cluster-navigator td:first').html('Multiplets')
            $window.trigger('resize')
            sv.draw()

    sv.on 'cluster-navigator-updated', () ->
      $('.jsv-cluster-navigator td:first').html('Multiplets')

    # Window Resize
    $window = $(window)
    $window.resize () ->
      main_width = $('.panel#spectrum .panel-body').width() - 1
      structure_width = $('#structure').width()
      sv.resize(main_width, null, true)
      cluster_section_width = main_width - structure_width - 4
      $('#assignments').width(cluster_section_width)
      $('#cluster-navigation').width(cluster_section_width - 2)

    if sv.structure
      sv.structure.on 'structure-resize', () ->
        main_width = $('.panel#spectrum .panel-body').width() - 1
        structure_width = $('#structure').width()
        cluster_section_width = main_width - structure_width - 4
        $('#assignments').width(cluster_section_width)
        $('#cluster-navigation').width(cluster_section_width - 2)

    # Show/hide Spectra viewer instructions
    $('#spectra-viewer-more-info').on 'click', () ->
      $('#spectra-viewer-instructions').slideToggle('fast')
      return false



