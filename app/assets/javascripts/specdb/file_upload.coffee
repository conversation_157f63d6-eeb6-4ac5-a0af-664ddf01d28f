# File upload functionality for peaklist files
on_ready ->
  initialize_peaklist_file_upload()

initialize_peaklist_file_upload = ->
  fileInput = $('#peaklist-file-input')
  fileUploadBtn = $('#file-upload-btn')
  clearFileBtn = $('#clear-file-btn')
  fileNameDisplay = $('#file-name-display')
  queryMassesTextarea = $('#query_masses')

  # Handle file upload button click
  fileUploadBtn.click (evt) ->
    evt.preventDefault()
    fileInput.click()

  # Handle file selection
  fileInput.change (evt) ->
    file = evt.target.files[0]
    if file
      # Validate file type
      if !file.name.toLowerCase().endsWith('.txt')
        alert('Please select a .txt file')
        fileInput.val('')
        return

      # Validate file size (limit to 1MB)
      if file.size > 1024 * 1024
        alert('File size must be less than 1MB')
        fileInput.val('')
        return

      # Display file name
      fileNameDisplay.text(file.name)
      clearFileBtn.show()

      # Read file content
      reader = new FileReader()
      reader.onload = (e) ->
        content = e.target.result
        
        # Validate content format and count lines
        lines = content.split(/\r?\n/).filter((line) -> line.trim().length > 0)
        
        # Check line limit
        if lines.length > 700  # Using the LIMIT_PEAK_COUNT from controller
          alert("File contains #{lines.length} lines. Maximum allowed is 700 lines.")
          clearFile()
          return

        # Validate each line format (should be numbers and optional whitespace)
        invalidLines = []
        for line, index in lines
          trimmedLine = line.trim()
          if trimmedLine.length > 0
            # Check if line contains valid numbers (mass and optional CCS)
            parts = trimmedLine.split(/\s+/)
            if parts.length > 2
              invalidLines.push(index + 1)
            else
              for part in parts
                # Basic check if part looks like a number
                if isNaN(parseFloat(part)) || !isFinite(parseFloat(part))
                  invalidLines.push(index + 1)
                  break

        if invalidLines.length > 0
          alert("Invalid format found on lines: #{invalidLines.join(', ')}. Each line should contain a mass value and optionally a CCS value separated by whitespace.")
          clearFile()
          return

        # If validation passes, populate the textarea
        queryMassesTextarea.val(content.trim())
        
      reader.onerror = ->
        alert('Error reading file')
        clearFile()

      reader.readAsText(file)

  # Handle clear button click
  clearFileBtn.click (evt) ->
    evt.preventDefault()
    clearFile()

  # Clear file function
  clearFile = ->
    fileInput.val('')
    fileNameDisplay.text('')
    clearFileBtn.hide()
    queryMassesTextarea.val('')

  # Handle manual textarea changes - hide file upload info if user types
  queryMassesTextarea.on 'input', ->
    if $(this).val().length > 0 && fileNameDisplay.text().length > 0
      # User is typing, clear file upload display but keep textarea content
      fileInput.val('')
      fileNameDisplay.text('')
      clearFileBtn.hide()
