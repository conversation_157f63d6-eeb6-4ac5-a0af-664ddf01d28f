var mz_plot_one_d = function(id,data,query_id,options){
  console.log(data)
  var padding    = 20;
  var axis_width = 40;
  var y_limit = 100; //Max y value, we scale to 100 as this is convention
  var height = 250; // Graph height
  options = typeof options !== 'undefined' ? options : {};
  console.log(query_id)
  // set up the html markup
  var chart_container = d3.select(id),
    chart_container_element = chart_container[0][0];
  chart_container.attr('class','chart_container');
  chart_container.attr('data-query-id',query_id);
  chart_container.append('div').attr('class','y_axis_label').text('Relative Intensity');
  var y_axis_element = chart_container.append('div').attr('class','y_axis')[0][0],
    chart_element  = chart_container.append('div').attr('class','chart')[0][0],
    x_axis_element = chart_container.append('div').attr('class','x_axis')[0][0];
  chart_container.append('div').attr('class','x_axis_label').text('Chemical Shift');
  console.log(chart_container_element)
  // This figures out the chart container size so we know how big to draw the graph
  var get_width = function() {
    // return 400;
    return chart_container_element - 2*padding - axis_width;
  }

  // This scales the data for the y-axis (intensity)
  var scale_data = function(d) {
    var max = d[0].y;
    // Get the max
    for (var i = 0; i < d.length; i++) {
      max = Math.max(max, d[i].y);
    }
    // Then scale the data using the max
    for (var i = 0; i < d.length; i++) {
      d[i].y = ( d[i].y / max ) * y_limit;
    }
    return d;
  }

  // Sort the spectra on x value for Rickshaw
  var sort_data = function(a, b) {
    return ((a.x < b.x) ? -1 : ((a.x > b.x) ? 1 : 0));
  }

  data = scale_data(data);
  data.sort(sort_data);

  // Calculate the min and max y-values
  var ymax = y_limit;
  var ymin = 0
  if (options.mirror) {
    ymin = ymax * -1;
  }

  var xmax = Number.MIN_VALUE;
  var xmin = Number.MAX_VALUE;
  for (i = 0; i < data.length; i++) {
    xmin = Math.max(xmin, data[i].x);
    xmax = Math.min(xmax, data[i].x);
  }

  var data_with_fragments = [],
  data_no_fragments = [],
  series;

  // If the data has structures, then it is a predict or assign query
  // Separate data into two series to differentiate peak with no fragments and peaks with fragments
  if(data[0].structure_ids) {
    for (var i = 0; i < data.length; i++) {
      if(data[i].structure_ids.length > 0) {
        data_with_fragments.push(data[i]);
      }
      else {
        data_no_fragments.push(data[i]);
      }
    }

    // Graph with no peaks with fragments in blue
    if (data_with_fragments.length < 1) {
      series = [{ color: "#52B4FA", data: data_no_fragments }];
    }
    // Graph with no peaks without fragments in red
    else if (data_no_fragments.length < 1) {
      series = [{ color: "#E63434", data: data_with_fragments }];
    }
    // Graph with peaks with and without fragments:
    // Peaks WITH fragments are red
    // Peaks WITHOUT fragments are blue
    else {
      series =  [
        { color: "#E63434", data: data_with_fragments },
        { color: "#52B4FA", data: data_no_fragments}
      ];
    }
    // Data with fragments is first in array
    window.data[id.substring(1)] = data_with_fragments.concat(data_no_fragments);
  }
  // If the data has no structures, then it is an identify query
  else {
    // Identify query with comparison candidate to graph
    // The input spectra is blue
    // The comparison candidate spectra is red

    if (window.compareCandidate && window.compareCandidate.peak_list) {
      var compare_data = scale_data(window.compareCandidate.peak_list);
      // Adjust max and mins for compare spectra if necessary
      for (i = 0; i < compare_data.length; i++) {
        xmin = Math.min(xmin, compare_data[i].x);
        xmax = Math.max(xmax, compare_data[i].x);
      }

      // If the option to mirror the data is selected, multiply
      // all the comparison y values by -1
      if (options.mirror) {

        compare_data = compare_data.map(function(dp) {
          return {x: dp.x, y: dp.y * -1, structure_ids: dp.structure_ids, structure_urls: dp.structure_urls, scores: dp.scores};
        });
        // Add labels for top and bottom spectra
        chart_container.append('div').attr('class','input_spectra_label').text('Input Spectra');
        chart_container.append('div').attr('class','candidate_spectra_label').text('Candidate Spectra');
      }
      series = [
        {
        color: "#E63434",
        data: compare_data,
        name: window.compareCandidate.compound
      },
      {
        color: "#52B4FA",
        data: data
      }
      ];

      // Store data in window
      window.data = compare_data.concat(data);
    }
    // Identify query without comparison candidate to graph
    // Just show input spectra in blue
    else {
      chart_container.append('div').attr('class','input_spectra_label').text('Input Spectra');
      chart_container.append('div').attr('class','candidate_spectra_label').text('Candidate Spectra');
      series = [ {color: "#52B4FA", data: data }, {color: "#52B4FA", data: data } ];
      window.data = data;
    }
  }
  var width = get_width();
  if (options.mirror) {
    height = height * 2;
  }

  // Now draw the graph
  var graph = new Rickshaw.Graph( {
    element: chart_element,
    width: width,
    height: height,
    xmin: xmin,
    renderer: 'linebar',
    name: 'Predicted Spectra',
    series: series.map(function(s) {
      // Make sure series is sorted on x or else rickshaw fails
      s.data = s.data.sort(function(a, b) {
        return a.x - b.x;
      } )
      return s;
    } )
  } );

  graph.xMin = options.xMin ||    xmax - 0.1;
  graph.xMax = options.xMax || 0;
  graph.yMin = options.yMin ||    ((options.mirror) ? ymin - 10 : 0);
  graph.yMax = options.yMax || ((options.mirror) ? ymax + 10 : 0);

  new Rickshaw.Graph.Axis.Y( {
    graph: graph,
    orientation: 'left',
    tickFormat: Rickshaw.Fixtures.Number.formatKMBT,
    ticks: ((options.mirror) ? 10 : 5),
    element: y_axis_element,
    grid: true,
    height: height
  } );

  new Rickshaw.Graph.Axis.X( {
    graph: graph,
    orientation: 'bottom',
    tickFormat: function(x) {
                  return x * -1
                },
    element: x_axis_element,
    grid: false
  } );

  new Rickshaw.Graph.HoverDetail(
    {
    graph: graph,
    formatter: function(series, x, y,formattedX, formattedY, d) {
      var structure_ids = d.value.structure_ids;
      var structure_img = "";
      var other_frags = "";
      //"Fragment unknown"
      if(structure_ids) {
        var structure = structure_ids[0];
        var structure_img = "Fragment unknown<br>";
        // If the structure has a valid number and image
        if(structure || structure == 0 || d.value.structure_urls[0]){
          structure_img = '<img class="structure" src="'+ d.value.structure_urls[0] +'" class="frag-structure" alt="Structure"><br>';
        }
        // Note if there is more than one possible structre
        if (structure_ids.length > 1) {
          other_frags = other_frags.concat("<br>" + (structure_ids.length - 1) + " other possible fragments.");
        }
      }
      // Add data to hover box
      var content;
      if (d.value.adduct_type) {
        content = "<div class=\"hover-box\" style=\"width: 150px;\">Adduct: " + d.value.adduct_type + "<br>chemical shift: " + x*-1 + "<br>intensity: " + y.toFixed(1).toString().replace("-", "") + "</div>";
      }
      else {
       content = "<div class=\"hover-box\" style=\"width: 150px;\">" + structure_img + "chemical shift: " + x*-1 + "<br>intensity: " + y.toFixed(1).toString().replace("-", "") + other_frags + "</div>";
      }
      return content;
    }
  } );

  var redraw = function() {

    graph.configure({
      width: get_width(),
    });
    graph.render();
  }

  // When loading spectra to compare
  var compare = function() {

    graph.configure({
      width: get_width(),
    });
    // Remove existing comparison spectra
    // graph.series.splice(0, 1);
    console.log(graph.series.splice(0, 1))
    if (window.compareCandidate.peak_list) {
      var compare_data = scale_data(window.compareCandidate.peak_list);
      // If the option to mirror the data is selected, multiply
      // all the comparison y values by -1
      if (options.mirror) {
        compare_data = compare_data.map(function(dp) {
          return {x: dp.x, y: dp.y * -1, structure_ids: dp.structure_ids, structure_urls: dp.structure_urls, scores: dp.scores};
        });
      }

      // Add new comparison spectra
      graph.series.unshift({
        color: "#E63434",
        data: compare_data,
        name: window.compareCandidate.compound
      });

      // Update the x-axis scale if necessary
      for (i = 0; i < compare_data.length; i++) {
        xmin = Math.min(graph.xMin, compare_data[i].x);
        xmax = Math.max(graph.xMax, compare_data[i].x);
      }
      graph.xMin = xmin;
      graph.xMax = xmax;

      window.data = compare_data.concat(graph.series[1]);
      graph.render();
    }
  }

  // Special adjustments for mz plots
  var adjust = function() {
    // Remove the - signs for all y-values
    if (options.mirror) {
      d3.selectAll("g.y_ticks text").html( function() {
        return $(this).html().replace("-", "");
      });
      // Style a center y-axis
      $("g.y_ticks g[transform='translate(0," + (height / 2) + ")'] text").html("0");
      $("g[data-y-value=''] line").css("stroke-dasharray", "none");
    }
  }

  window.addEventListener('resize', redraw); 
  window.addEventListener('compare', compare); 
  //redraw();

  graph.render();
  adjust();
  // Callback to adjust whenenver graph is re-rendered
  graph.onUpdate(adjust);

}
