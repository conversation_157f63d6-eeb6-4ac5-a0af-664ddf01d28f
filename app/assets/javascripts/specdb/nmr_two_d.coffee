on_ready ->
  if $('#nmr-spectrum-2d').length
    path = d3.select('#nmr-spectrum-2d').attr('data-path')
    data_type = d3.select('#nmr-spectrum-2d').attr('data-type')

    if data_type == 'json'
      sv = new JSV.SpectraViewer2D '#nmr-spectrum-2d', {
        debug: false,
        width: $('.panel#spectrum .panel-body').width(),
        height: "800",
        zoom_min: 0.1
      }
      sv.flash('Loading...')

      $.getJSON path, (data) ->
        data = JSON.parse(data)
        # Axis are backwards in the JSON files so we need to flip them
        temp = data['x']
        data['x'] = data['y']
        data['y'] = temp
        sv.add_spectrum_2d({
          json_data: data
        })

      $window = $(window)
      $window.resize () ->
        main_width = $('.panel#spectrum .panel-body').width() - 1
        structure_width = $('#structure').width()
        cluster_section_width = main_width - structure_width - 4
        sv.resize(main_width, null, true)
        $('#assignments').width(cluster_section_width)
        $('#cluster-navigation').width(cluster_section_width - 2)
        sv.draw()

      if sv.structure
        sv.structure.on 'structure-resize', () ->
          main_width = $('.panel#spectrum .panel-body').width() - 1
          structure_width = $('#structure').width()
          cluster_section_width = main_width - structure_width - 4
          $('#assignments').width(cluster_section_width)
          $('#cluster-navigation').width(cluster_section_width - 2)
          sv.draw()

    else if data_type == 'nmrml'
#      TODO: Add structure from nmrML file to 2D viewer
      sv = new JSV.SpectraViewer2D '#nmr-spectrum-2d', {
        debug: false,
        width: $('.panel#spectrum .panel-body').width(),
        height: "800",
        zoom_min: 0.1
      }
      sv.flash('Loading...')
      $.ajax
        dataType: 'text',
        url: path,
        success: (nmrml) ->
          if nmrml
            sv.add_nmrml_data(nmrml)
            $('.jsv-cluster-navigator td:first').html('Multiplets')
            $window.trigger('resize')
            sv.draw()

      $window = $(window)
      $window.resize () ->
        main_width = $('.panel#spectrum .panel-body').width() - 1
        structure_width = $('#structure').width()
        cluster_section_width = main_width - structure_width - 4
        sv.resize(main_width, null, true)
        $('#assignments').width(cluster_section_width)
        $('#cluster-navigation').width(cluster_section_width - 2)
        sv.draw()

      if sv.structure
        sv.structure.on 'structure-resize', () ->
          main_width = $('.panel#spectrum .panel-body').width() - 1
          structure_width = $('#structure').width()
          cluster_section_width = main_width - structure_width - 4
          $('#assignments').width(cluster_section_width)
          $('#cluster-navigation').width(cluster_section_width - 2)
          sv.draw()