Rickshaw.namespace('Rickshaw.Graph.Renderer.LineBar');

Rickshaw.Graph.Renderer.LineBar = Rickshaw.Class.create( Rickshaw.Graph.Renderer, {

        name: 'linebar',

        defaults: function($super) {
 

                return Rickshaw.extend( $super(), {
                        unstack: true,
                        fill: true,
                        stroke: false,
                        padding:{ top: 0.01, right: 0.01, bottom: 0.01, left: 0.01 },
                        dotSize: 4
                } );
        },

        initialize: function($super, args) {
                $super(args);
        },

        domain: function(args) {
          var graph = this.graph;
          return {x: [graph.xMin,graph.xMax], y: [graph.yMin,graph.yMax]};
        },

        render: function(args) {

                args = args || {};

                var graph = this.graph;

                var series = args.series || graph.series;
                var vis = args.vis || graph.vis;

                var dotSize = this.dotSize;

                vis.selectAll('*').remove();

                series.forEach( function(series) {

                        if (series.disabled) return;

                        var nodes = vis.selectAll("path")
                                .data(series.stack.filter( function(d) { return d.y !== null } ))
                                .enter().append("svg:line")
                                        .attr("x1", function(d) { return graph.x(d.x) })
                                        .attr("x2", function(d) { return graph.x(d.x) })
                                        .attr("y1", function(d) { return graph.y(0) })
                                        .attr("y2", function(d) { return graph.y(d.y) })
                                        .attr("class","graphline")

                        if (series.className) {
                                nodes.classed(series.className, true);
                        }

                        Array.prototype.forEach.call(nodes[0], function(n, i) {
                                n.setAttribute('fill', series.color);
                                n.setAttribute('style', 'stroke: ' + series.color);
                        } );

                }, this );
        }
} );


