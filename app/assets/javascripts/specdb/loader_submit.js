function submit_button_click(){
    $('#load-screen').show()

    $.ajax({
        complete: function(){
            $('#load-screen').hide()
        }
    })
}

function search_button_click(){
    $('#load-screen').show()
}

function mass_search_button_click(){
    $('#load-screen').show()
    if ($('#query_from').val() != "" && $('#query_to').val() != "")
        $('#load-screen').show()
    else 
        $.ajax({
            complete: function(){
                $('#load-screen').hide()
            }
        })

}

function validateNmrSearch() {
    let isValid = true;
    if (!isValidPeakList()) {
        displayPeakListErrors();
        isValid = false;
    }

    if(isValid) {
        $('load_screen').show()
    }

    return isValid;
}


function isValidPeakList() {
    let re = /[+-]?([0-9]*[.])?[0-9]+[\r\n]?/;
    if(!$('#peaks').val()) {
        return false;
    } else if (!re.test($('#peaks').val().trim())) {
        return false;
    }
    return true;
}


function displayPeakListErrors() {
    showPeakListError();
    highlightPeakList();
}

function showPeakListError() {
    if(!$('#peaks').val()) {
        $('#chemical-shift-error').html("You must enter at least one chemical shift!")
    } else {
        $('#chemical-shift-error').html("Please check your input! Check example for required input formatting.")
    }
}

function highlightPeakList() {
    setTimeout(function() {
        $('#peaks').addClass('active');
    }, 100);
    $('#peaks').removeClass('active').addClass('final').trigger('focus').select();
}


function isNumberKey(evt){
    let charCode = (evt.which) ? evt.which : evt.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57));
}