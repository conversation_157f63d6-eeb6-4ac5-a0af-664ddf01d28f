on_ready ->

  $('table.ms_cmm_table').dataTable()

  # Apply the datatable to tables
  $('div.ms-search-table table').each (index,table) ->
    $(table).dataTable {
      aaSorting: [[6,"asc"]]
    }

  # Update list of adduct types based on which ion mode is selected
  ion_mode_selector = (ion_mode_selector,adduct_type_selector) ->
    $(ion_mode_selector).change (e) ->
      selected_ion_mode = $("option:selected",this).text()
      adduct_select = $(adduct_type_selector)

      # Get the appropriate adduct list
      adduct_list =
        switch selected_ion_mode
          when 'Positive' then adduct_select.data('positive-adducts')
          when 'Negative' then adduct_select.data('negative-adducts')
          when 'Neutral' then adduct_select.data('neutral-adducts')

      # Set the new list
      adduct_select.empty()
      for adduct in adduct_list
        option = $('<option></option>').attr("value", adduct).text(adduct)
        adduct_select.append(option);

      # Remember which adduct type was selected, if returning from search
      if adduct_select.data('remember-selected')
        adduct_select.val(adduct_select.data('remember-selected').split(' '))

      e.preventDefault()

  ion_mode_selector('#ms_search_ion_mode','#adduct_type')
  $("#ms_search_ion_mode").trigger "change" # Force selection on page load

  # ion_mode_selector('#ms_ms_search_ion_mode','#adduct_type')
  # $("#ms_ms_search_ion_mode").trigger "change" # Force selection on page load
