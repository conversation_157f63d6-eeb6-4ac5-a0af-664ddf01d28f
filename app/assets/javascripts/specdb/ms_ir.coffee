on_ready ->
  if $('#ir-spectrum').length
    path = d3.select('#ir-spectrum').attr('data-path')
    sv = new JSV.SpectraViewer '#ir-spectrum', {
      width: $('.panel#spectrum .panel-body').width(),
      cluster_navigation_id: '#cluster-navigation',
      simplify_tolerance: 0.01,
      zoom_max: 1000,
      min_boundaries: {x: [0, 1], y: [-1, 1.19]},
    }
    sv.flash('Loading...')
    window.sv = sv
    $.ajax
      dataType: 'text',
      url: path,
      success: (data) ->
        sv.add_ir_data(data)

    $window = $(window)
    $window.resize () ->
      main_width = $('.panel#spectrum .panel-body').width() - 1
      sv.resize(main_width, null, true)