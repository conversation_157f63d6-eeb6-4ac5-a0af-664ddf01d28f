// File upload functionality for peaklist files
$(document).ready(function() {
  initializePeaklistFileUpload();
});

// Also try with turbolinks
$(document).on('page:load', function() {
  initializePeaklistFileUpload();
});

function initializePeaklistFileUpload() {
  console.log('Initializing peaklist file upload...');
  
  var fileInput = $('#peaklist-file-input');
  var fileUploadBtn = $('#file-upload-btn');
  var clearFileBtn = $('#clear-file-btn');
  var fileNameDisplay = $('#file-name-display');
  var queryMassesTextarea = $('#query_masses');

  console.log('Found elements:', {
    fileInput: fileInput.length,
    fileUploadBtn: fileUploadBtn.length,
    clearFileBtn: clearFileBtn.length,
    fileNameDisplay: fileNameDisplay.length,
    queryMassesTextarea: queryMassesTextarea.length
  });

  // Handle file upload button click - this is now handled by inline onclick
  // but keeping this as backup
  fileUploadBtn.off('click').on('click', function(evt) {
    console.log('File upload button clicked via jQuery');
    evt.preventDefault();

    // Test if the file input exists and is accessible
    if (fileInput.length === 0) {
      alert('File input not found!');
      return;
    }

    fileInput.click();
  });

  // Handle file selection
  fileInput.off('change').on('change', function(evt) {
    console.log('File input changed');
    var file = evt.target.files[0];
    if (file) {
      console.log('File selected:', file.name);

      // Simple validation for now
      if (!file.name.toLowerCase().endsWith('.txt')) {
        alert('Please select a .txt file');
        fileInput.val('');
        return;
      }

      if (file.size > 1024 * 1024) {
        alert('File size must be less than 1MB');
        fileInput.val('');
        return;
      }

      // Display file name and show clear button
      fileNameDisplay.text(file.name);
      clearFileBtn.show();

      // Read and load file content
      var reader = new FileReader();
      reader.onload = function(e) {
        var content = e.target.result;
        console.log('File content loaded, length:', content.length);
        queryMassesTextarea.val(content.trim());
        console.log('File content loaded into textarea');
      };

      reader.onerror = function() {
        alert('Error reading file');
        clearFile();
      };

      reader.readAsText(file);
    }
  });

  // Handle clear button click
  clearFileBtn.off('click').on('click', function(evt) {
    console.log('Clear button clicked');
    evt.preventDefault();
    clearFile();
  });

  // Clear file function
  function clearFile() {
    fileInput.val('');
    fileNameDisplay.text('');
    clearFileBtn.hide();
    queryMassesTextarea.val('');
  }

  // Handle manual textarea changes - hide file upload info if user types
  queryMassesTextarea.off('input.fileupload').on('input.fileupload', function() {
    if ($(this).val().length > 0 && fileNameDisplay.text().length > 0) {
      // User is typing, clear file upload display but keep textarea content
      fileInput.val('');
      fileNameDisplay.text('');
      clearFileBtn.hide();
    }
  });
}
