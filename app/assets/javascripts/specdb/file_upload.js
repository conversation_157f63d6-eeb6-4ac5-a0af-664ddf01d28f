// File upload functionality for peaklist files
$(document).ready(function() {
  initializePeaklistFileUpload();
});

// Also try with turbolinks
$(document).on('page:load', function() {
  initializePeaklistFileUpload();
});

function initializePeaklistFileUpload() {
  console.log('Initializing peaklist file upload...');

  // MS Search
  initializeFileUpload('peaklist-file-input', 'file-upload-btn', 'clear-file-btn', 'file-name-display', 'query_masses');

  // MS/MS Search
  initializeFileUpload('msms-peaklist-file-input', 'msms-file-upload-btn', 'msms-clear-file-btn', 'msms-file-name-display', 'peaks');

  // GC-MS Search
  initializeFileUpload('gcms-peaklist-file-input', 'gcms-file-upload-btn', 'gcms-clear-file-btn', 'gcms-file-name-display', 'peaks');

  // NMR Search - Peaks
  initializeFileUpload('nmr-peaks-file-input', 'nmr-peaks-file-upload-btn', 'nmr-peaks-clear-file-btn', 'nmr-peaks-file-name-display', 'peaks');

  // NMR Search - Intensities
  initializeFileUpload('nmr-intensities-file-input', 'nmr-intensities-file-upload-btn', 'nmr-intensities-clear-file-btn', 'nmr-intensities-file-name-display', 'intensities');
}

function initializeFileUpload(fileInputId, uploadBtnId, clearBtnId, displayId, textareaId) {
  var fileInput = $('#' + fileInputId);
  var fileUploadBtn = $('#' + uploadBtnId);
  var clearFileBtn = $('#' + clearBtnId);
  var fileNameDisplay = $('#' + displayId);
  var textarea = $('#' + textareaId);

  // Skip if elements don't exist (not on this page)
  if (fileInput.length === 0 || fileUploadBtn.length === 0) {
    return;
  }

  console.log('Found elements for ' + fileInputId + ':', {
    fileInput: fileInput.length,
    fileUploadBtn: fileUploadBtn.length,
    clearFileBtn: clearFileBtn.length,
    fileNameDisplay: fileNameDisplay.length,
    textarea: textarea.length
  });

  // Handle file selection
  fileInput.off('change').on('change', function(evt) {
    console.log('File input changed');
    var file = evt.target.files[0];
    if (file) {
      console.log('File selected:', file.name);

      // Simple validation for now
      if (!file.name.toLowerCase().endsWith('.txt')) {
        alert('Please select a .txt file');
        fileInput.val('');
        return;
      }

      if (file.size > 1024 * 1024) {
        alert('File size must be less than 1MB');
        fileInput.val('');
        return;
      }

      // Display file name and show clear button
      fileNameDisplay.text(file.name);
      clearFileBtn.show();

      // Read and load file content
      var reader = new FileReader();
      reader.onload = function(e) {
        var content = e.target.result;
        console.log('File content loaded, length:', content.length);
        textarea.val(content.trim());
        console.log('File content loaded into textarea');
      };

      reader.onerror = function() {
        alert('Error reading file');
        clearFile();
      };

      reader.readAsText(file);
    }
  });

  // Handle clear button click
  clearFileBtn.off('click').on('click', function(evt) {
    console.log('Clear button clicked');
    evt.preventDefault();
    clearFile();
  });

  // Clear file function
  function clearFile() {
    fileInput.val('');
    fileNameDisplay.text('');
    clearFileBtn.hide();
    textarea.val('');
  }

  // Handle manual textarea changes - hide file upload info if user types
  textarea.off('input.fileupload').on('input.fileupload', function() {
    if ($(this).val().length > 0 && fileNameDisplay.text().length > 0) {
      // User is typing, clear file upload display but keep textarea content
      fileInput.val('');
      fileNameDisplay.text('');
      clearFileBtn.hide();
    }
  });
}
