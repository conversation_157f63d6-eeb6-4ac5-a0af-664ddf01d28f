.specdb-sidenav {
  .affix {
    // margin-top: -90px;
  }
}

.specdb {
  .panel-heading { font-weight: bold; }
  .spec-table {
    th {
      text-align: right;
    }
    th, td {
      padding: 0px 5px;
    }
  }
  .unpadded-table {
    margin-bottom: 0px;
  }
  div.spectrum-chart {
    margin: 30px;
    border: solid #CCC 1px;
    border-radius: 5px;
    overflow: hidden;
    width: 820px;
    background: #fff;
    position: relative;

    .spectrum  {
      margin-left: 0;
      border-right: solid #CCC 1px;
      padding: 30px;
      height: 392px;
      width: 719px;
      box-shadow: inset 0px 0px 20px #DDD;
      background: #fff;
      float: left;

      p.instructions {
        font-style: italic;
        color: #999;
        font-size: 12px;
      }

      h1 {
        line-height:18px;
        margin:0;
      }

      .rule {
        stroke: #eee;
      }

      .rule text, .axis text {
        stroke: black;
        stroke-width: 0;
        color: black;
        font-weight: normal;
        font: 10px sans-serif;
      }

      line.peak  {
        stroke: #2C17B1;
      }

      line.peak.selected, line.peak:hover {
        stroke-width: 4;
        stroke: #2DD7DD;
      }

      circle.peak {
        fill: rgba(51,204,205,0.25);
        stroke: #999;
        stroke-width: 1px;
      }

      circle.peak.selected {
        fill: #2DD7DD;
      }

      .brush rect.extent {
        fill: steelblue;
        fill-opacity: .125;
      }

      .brush .resize path {
        fill: #eee;
        stroke: #666;
      } 

      .axis path,
      .axis line {
        fill: none;
        stroke: black;
        shape-rendering: crispEdges;
      }

      text.title {
        font-size: 12px;
      }
    }

    .peak-x-label {
      text {
        font: 10px sans-serif;
      }
    }

    .toolbar {
      float: left;
      width: 40px;
      height: 452px;
      position: relative;
      background: #eee;
      border-top-right-radius: 5px;

      div.btn {
        height: 50%;
        cursor: hand; 
        cursor: pointer;
      }

      div.btn.next {
        background: url('/assets/specdb/monotone_arrow_right.png') no-repeat center center;
        border-bottom: 1px solid #CCC;		
      }

      div.btn.prev {
        background: url('/assets/specdb/monotone_arrow_left.png') no-repeat center center;
      }
      div.btn.pressed {
        box-shadow: inset 2px 0 10px #BBB;
      }
    }

    .peak-assignment {
      position: absolute;
      right: 67px;
      top: 67px;
      border: 1px solid lightGrey;
      box-shadow: 3px 3px 10px lightGrey;
      background: white;
      padding: 0 15px;

      img {
        width: 150px;
      }

      p {
        text-align: center;
        margin: 10px;
      }

      table { margin-right: -10px; }

      td {
        min-width: 60px;
        text-align: right;
      }

      th {text-align: left;}

      h2 {
        margin-top: 0;
        margin-bottom: 0;
        border-right: none;
        border-top: none;
        border-left: none;
      }
    }

    .peak-assignment.hidden {
      display: none;
    }
  }

  div.search-form {
    position: relative;
    border: 1px solid lightGrey;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-right: 100px;
    margin-left: 100px;
    padding: 30px;
    padding-bottom: 15px;
    padding-top: 0;
    overflow: hidden;
    font-size: 12px;

    h1 {
      font-size: 20px;
    }

    p.info {
      color: #888;
      font-style: italic;
      background: transparent;
      border: none;
    }

    label {
      font-size: 12px;
      padding-top: 10px;
      padding-bottom: 10px;
    }

    div.left-col, div.right-col {
      float: left;
      margin: 3px;
    }

    div.left-col {
      margin-right: 30px;
      border-right: 1px solid lightGrey;
      padding-right: 30px;
      width: 250px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    

      td { 
        padding: 5px;
        vertical-align: top;
      }

      textarea {
        height: 100px;
      }
    }

    div.footer {
      clear: none;
      padding: 10px;
      position: absolute;
      bottom: 15px;
      right: 15px;
    }

  }

  textarea#query_masses {
    width: 200px;
    height: 20em;
    margin-top: 10px;
  }

  div.ms-search-result {
    border: 1px solid lightGrey;
    padding: 3px;
    background: #EFEFEF;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 1.5em;

    span.title {
      font-size: 14px;
      font-weight: bold;
      padding-right: 25px;
      color: #21536A;
      margin-left: 3px
    }

    span.info, span.jtable-page-info, span.jtable-page-number-active {
      font-size: 13px;
      color: #777;
      font-style: italic;	
      background: transparent;
      border: none;
    }

    span.jtable-page-number-active {
      color: #333;
      font-style: normal;
    }

    span.jtable-page-info {
      float: right;
    }

    .jtable-busy-message {
      background: grey;
      color: white;
      font-weight: bold;
      padding: 10px;
    }
  }

  #ms-export-csv {
    background: #eee;
    border: 1px solid #CCC;
    padding: 10px;
  }

  span.jtable-page-number, span.jtable-page-number-previous,
  span.jtable-page-number-first, span.jtable-page-number-active,
  span.jtable-page-number-next, span.jtable-page-number-last {
    border: 1px solid #999;
    background: white;
    padding: 2px;
    padding-right: 7px;
    padding-left: 7px;
    margin-right: 3px;
  }

  span.jtable-page-number:hover,  span.jtable-page-number-previous:hover, 
  span.jtable-page-number-first:hover,  span.jtable-page-number-active:hover, 
  span.jtable-page-number-next:hover,  span.jtable-page-number-last:hover {
    background: #ffffeb;
  }

  span.jtable-page-number-active {
    background: lightBlue;
  }

  table.jtable {
    border-spacing: 0;


    td.name {
      max-width: 200px;
    }

    tr {
      border-right: 1px solid lightGrey;
    }

    tr { background: #fff }
    tr:nth-child(odd) { background: #eee; }
    tr:hover, tr:nth-child(odd):hover { background: #ffffeb;}

    th {
      border: 1px solid #fff;
      font-weight: normal;
      text-align: center;
    }

    td {
      border: 0;
      padding: 2px;
      padding-left: 5px;
      padding-right: 5px;
    }

    td.right {
      text-align: right;
    }

  }

  div.ms-search-table table.dataTable th {
    background-color: #56697A;
  }

  td.control { text-align: center }
}
