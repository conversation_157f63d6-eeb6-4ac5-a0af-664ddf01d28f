.specdb-search-form {
  @extend .well;
  margin-top: 1em;
  form {
    width: 100%;
    max-width: 1000px;
    input[type="text"], input[type="number"] {
      max-width: 315px;
    }
    textarea {
      max-width: 400px;
    }
  }

  // label, input {
  //   display: block;
  // }

  label.label-vertical {
    width: 400px;
    text-align: right;
    padding-top: 5px;
  }

  label.text-area-label {
    text-align: left;
  }

  .form-group .help-block {
    font-size: 12px;
  }

  .mac-logo {
    display: inline;
    height: 17px;
    position: relative;
    top: -2px;
  }

  .windows-logo {
    display: inline;
    height: 17px;
    position: relative;
    top: -1px;
  }

  .bottom-padding {
    margin-bottom: 10px;
  }

  .label-horizontal {
    display: inline-block;
  }

  .load-search-buttons {
    padding-left: 100px;
  }

  .reset-button {
    float: right;
  }

  @media only screen and (max-width: 700px) {
    label.label-vertical {
      text-align: left;
    }
    .reset-button, .load-search-buttons {
      float: left;
      padding-left: 0px;
    }
  }

  .nmr-search-input-col {
    white-space: nowrap;
    display: inline-block;

    .form-control{
      display: inline;
    }
    i{
      display: inline;
      margin: 5px;
    }

    .tooltip-inner{
      text-align: left;
    }
  }

  .nmr-search-buttons{
    button{
      margin-right: 5px;
    }
  }

}

.label-padded {
  padding-top: 27px;
}

input.faux-inline {
  display: inline;
  width: 65%;
}
select.faux-inline {
  display: inline;
  width: 33%;
  position: relative;
  top: -1px;
  margin-left: 5px;
}

label.secondary-label {
  display: block !important;
  float: left !important;
  text-align: left !important;
}

.ion-select {
  height: 135px !important;
}

.results-table {
  td:first-child {
    text-align: left;
    vertical-align: middle;
    position: relative;
    a.btn-card {
      width: 80%;
    }
    a.btn-compare {
      width: 80%;
    }
    .type {
      float: center;
      position: absolute;
      top: 0;
      padding: 2px 10px;;
      margin: 5px;
      border-radius: 5px;
      color: white;
      font-size: 11px;
      text-transform: uppercase;
      line-height: 1.5;
    }
    .predicted {
      background-color: #3059AC;
    }
    .experimental {
      background-color: #339533;
    }
  }
}

.calculator-table {
  width: 100%;
  border-collapse: collapse;
  tr:nth-of-type(odd) {
  background: #eee;
  }
  th {
  background: #333;
  color: white;
  font-weight: bold;
  }
  td, th {
  padding: 6px;
  border: 1px solid #ccc;
  text-align: left;
  }
}

#calculator-details.modal {
  margin-top: 60px;
}


.btn-calculator-details {
  @extend .btn;
  @extend .btn-xs;
  @extend .btn-calc;
}

.btn-calc {
      border: 1px solid #b2b2b2;
      border-radius: 5px;
      background-color: #ffffff;
      color: black;
}

.btn-calc:hover {
      border: 1px solid #b2b2b2;
      background-color: #e5e5e5;
      color: Black;
}

ul.nav-tabs {
  margin-bottom: 1em;
}

.scrollWrapper { overflow:scroll; }
.search-spectrum {
  position: relative;
  overflow: hidden;
  .spectrum-overlay {
    display: none;
    background-color: rgba(128,128,128,0.5);
    position: absolute;
    height: calc(100% - 40px);
    width: 100%;
    z-index: 100;
    text-align: center;
    img {
      margin-top: 210px;
    }
  }
  .search-color {
    font-weight: bold;
    color: #52B4FA;
  }
  .compare-color {
    font-weight: bold;
    color: #E63434;
  }
  .search-legend {
    border: 1px solid grey;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background-color: lightgrey;
    text-align: center;
    margin-bottom: 1rem;
    .legend-item {
      display: inline-block;
      padding-right: 1rem;
      .search-color-box {
        width: 12px;
        height: 12px;
        display: inline-block;
        background-color: #52B4FA;
        margin-right: 0.25rem;
      }
      .compare-color-box {
        width: 12px;
        height: 12px;
        display: inline-block;
        background-color: #E63434;
        margin-right: 0.25rem;
      }
      #comparison-compound {
        color: #E63434;
        font-weight: bold;
      }
    }
  }
}

.input_spectra_label {
  text-align: center;
  width: 100%;
  position: absolute;
  top: 20px;
  font-size: 1em;
  color: #52B4FA;
  font-weight: bold;
}
.candidate_spectra_label {
  text-align: center;
  width: 100%;
  position: absolute;
  bottom: 66px;
  font-size: 1em;
  color: #E63434;
  font-weight: bold;
}

.btn-card.btn-current {
  border: 1px solid #9f1414;
  background-color: #b61616;
  border-radius: 3px;
  width: 80%;
  margin-top: 0.25rem;
  color: white;
  &:hover, &:focus, &:active {
    color: white;
    background-color: #e31c1c;
  }
}

.btn-card.btn-compare {
  border: 1px solid #209ef8;
  background-color: #52B4FA;
  border-radius: 3px;
  width: 80%;
  margin-top: 0.25rem;
  color: white;
  &:hover, &:focus, &:active {
    color: white;
    background-color: #209ef8;
    border: 1px solid #209ef8;
  }
}

.scrollWrapper { 
  overflow: scroll; 
}

/*
 * width and overflow
 * inline-block elements expand as much as content
 * relative position makes z-index work
 * explicit width and nowrap makes overflow work
 */
.truncate {
  display: inline-block;
  position: relative;
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*
 * higher z-index brings element to front
 * auto width cancels the overflow
 */
.truncate:hover {
  z-index: 1;
  width: auto;
  background-color: #FFFFCC;
}

.tab {
  margin-left: 20px;
  margin-bottom: 0px;
}

.adv-num-box {
  width: 20%;
  height: 10%;
}

.peaks-search-text {
  padding: 1em 0;
  font-size: 1.25em;
  font-weight: bold;
}

.default-hidden {
  display: none;
}

#range-over-200 {
  margin: 1em 0;
}

div.ui-slider-range.ui-widget-header {
  background: green;
}

.help:hover {
  cursor: help;
}

.invalid-input {
  border:  2px inset red;
  color: red;
}

.warning-input {
  border:  2px inset #eed202;
  color: #eed202;
}

.invalid-input:focus {
  color: red;
  outline-color: red;
}

@keyframes highlight {
  50%  {background-color: red;}
  100% { background-color: white; }
}

textarea#peaks {
  outline: 0;
}

textarea.active#peaks {
  background-color: white;
  animation-name: highlight;
  animation-duration: .1s;
}

textarea.final#peaks {
  border:1px solid red;
}

textarea.final::selection#peaks {
  background: red;
}

textarea.final::-moz-selection#peaks {
  background: red;
}

#chemical-shift-error {
  padding: 0.5em 0;
  color: red;
}

#nmr-right-form {
  margin-top: 1.8em;
}

#error-ctn {
  font-size: large;
  font-weight: bold;
  color: red;
}