require 'active_resource'
require "specdb/engine"
require "specdb/version"
require "specdb/configuration"
require "specdb/has_spectra"
require "specdb/mz_ml"
require "specdb/has_compound"
require "specdb/rescue_helper"

module Specdb
  
  def self.config
    config_file = File.join( Rails.root, "config", "specdb.yml" ) 
    @config ||= Specdb::Configuration.new(config_file)
  end

  # Load into Rails application
  ActiveRecord::Base.send :include, Specdb::HasSpectra
end
