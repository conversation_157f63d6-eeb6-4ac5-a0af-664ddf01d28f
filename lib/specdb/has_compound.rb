module Specdb
  module <PERSON><PERSON><PERSON><PERSON>und 
    extend ActiveSupport::Concern

    # This method uses the inchi_key in the result to look 
    # up the compound in the app. The compound

    # Need to only query exported compounds To DO::
    def compound
      compound =
        if "moldb_inchikey" == Specdb.config.inchi_key_column.to_s 
         Specdb.config.compound_class.
           where("moldb_inchikey = 'InChIKey=#{inchi_key}' OR moldb_inchikey = '#{inchi_key}'").first
        else
          Specdb.config.compound_class.
            where( Specdb.config.inchi_key_column => inchi_key ).first
        end

      # Wrap the compound object with a compound_decorator that
      # adds the ability to call the moldb_methods with or without the
      # moldb_ prefix
      compound.nil? ? nil : CompoundDecorator.new(compound)
    end

  end
end
