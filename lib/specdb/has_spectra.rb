module Specdb::HasSpectra
  extend ActiveSupport::Concern

  module ClassMethods
    def has_spectra(options={})
      spectra_types = [ Specdb::CMs, Specdb::EiMs, Specdb::NmrOneD,
                        Specdb::MsMs, Specdb::NmrTwoD, Specdb::MsIr]
      spectra_types.each do |klass|
        association_name = (klass.to_s.sub(/Specdb::/,"") + "Spectrum").tableize
        define_method association_name.to_sym do
          h = klass.find :all, :params => { :database_id => _specdb_database_id }
        end
      end

      define_method :spectra do
        Specdb::Spectrum.find_all_by_database_id( _specdb_database_id) || Array.new
      end

      define_method :_specdb_database_id do
        self.send Specdb.config.compound_id_column
      end

      define_method :raw_inchi_key do
        @raw_inchi_key ||=

          if respond_to? :moldb_inchikey then moldb_inchikey
          elsif respond_to? :inchi_key then inchi_key
          elsif respond_to? :inchikey  then inchikey
          else raise "Could not find inchikey which is needed to find associated spectra"
          end.tap do |key|
            unless key.nil?
              key.strip!
              key.sub!( /inchikey=/i, "" )
            end
          end
      end
    end

  end
end
