module Specdb
  module Configurable
    class Configuration
      class << self
        attr_reader :settings, :default_settings

        # Use this to define the list of settings
        def configurable(*args)
          @settings = args
          define_setting_readers!
        end

        # Use this to define the default settings.
        # Settings that are defined but not configured
        # here must be configured by the user or an
        # error is thrown
        def default_configuration(defaults={})
          @default_settings = defaults
        end

        def required_settings
          @settings - @default_settings.keys
        end

        # Add methods for reading settings
        def define_setting_readers!
          @settings.each do |setting|
            define_method setting do 
              setting_reader setting
            end
          end
        end
      end

      # Read configuration from <tt>config/specdb.yml</tt>.
      def initialize(config_file)
        # Load user configuration
        user_configuraton  = YAML.load_file(config_file) 

        # Store user configuration for current environment
        @user_configuraton = 
          (user_configuraton && user_configuraton[Rails.env]) || Hash.new 


        # Raise errors for required settings that are missing
        self.class.required_settings.each do |setting|
          unless @user_configuraton[setting.to_s]
            raise( ArgumentError, "missing #{setting} in SpecDB config" )       
          end
        end
      end

      def setting_reader(setting)
        @user_configuraton[setting.to_s] || self.class.default_settings[setting]
      end
    end
  end
end
