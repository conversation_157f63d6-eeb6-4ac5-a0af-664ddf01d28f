module Specdb
  module MzMl
    extend ActiveSupport::Concern

    def to_mz_ml(request)
      inchikey = "N/A"
      if !self.compound.nil?
        inchikey = self.compound.inchikey
      end
      txt = self.documents.find { |d| d.description.downcase.include? "list of m/z values for the spectrum" }
      raise "Peak List not found" if txt.blank?
      builder = Nokogiri::XML::Builder.new do |xml|
        xml.mzML  "xmlns" => "http://psi.hupo.org/ms/mzml",
                  "xmlns:xsi" => "http://www.w3.org/2001/XMLSchema-instance",
                  "xsi:schemaLocation" => "http://psi.hupo.org/ms/mzml http://psidev.info/files/ms/mzML/xsd/mzML1.1.0.xsd",
                  # "id" => self.structure.to_param,
                  "id" => "#{inchikey}",
                  "version" => "0.1" do
          xml.cvList "count" => "2" do
            xml.cv  "id" => "MS",
                    "fullName" => "Proteomics Standards Initiative Mass Spectrometry Ontology",
                    "version" => "2.26.0",
                    "URI" => "http://psidev.cvs.sourceforge.net/*checkout*/psidev/psi/psi-ms/mzML/controlledVocabulary/psi-ms.obo"
            xml.cv  "id" => "UO",
                    "fullName" => "Unit Ontology",
                    "version" => "14:07:2009",
                    "URI" => "http://obo.cvs.sourceforge.net/*checkout*/obo/obo/ontology/phenotype/unit.obo"
          end

          xml.fileDescription do
            xml.fileContent do
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000127", "name" => "centroid spectrum", "value" => ""
            end
            xml.sourceFileList "count" => "1" do
              xml.sourceFile "id" => "_" + "#{File.basename(URI.parse(txt.url).path)}", "name" => "#{File.basename(URI.parse(txt.url).path)}", "location" => "http://#{request.host}#{txt.url}" do
                xml.cvParam "cvRef" => "MS", "accession" => "MS:1001369", "name" => "text file", "value" => ""
              end
            end
            xml.contact do
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000586", "name" => "contact name", "value" => "David Wishart"
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000590", "name" => "contact organization", "value" => "University of Alberta"
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000587", "name" => "contact address", "value" => "University of Alberta, Edmonton, Alberta, Canada, T6G 2E8"
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000588", "name" => "contact URL", "value" => "http://wishartlab.com"
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000589", "name" => "contact email", "value" => "<EMAIL>"
            end
          end
          xml.softwareList "count" => "1" do
            xml.software "id" => "SpecDB", "version" => "0.1" do
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000799", "name" => "custom unreleased software tool", "value" => "SpecDB Spectra management database"
            end
          end
          xml.instrumentConfigurationList "count" => "1" do
            xml.instrumentConfiguration "id" => "instrument" do
              xml.cvParam "cvRef" => "MS", "accession" => "MS:1000031", "name" => "instrument model", "value" => ""
            end
          end
          xml.dataProcessingList "count" => "1" do
            xml.dataProcessing "id" => "SpecDB_processing" do
              xml.processingMethod "order" => "1", "softwareRef" => "SpecDB" do
                xml.cvParam "cvRef" => "MS", "accession" => "MS:1000544", "name" => "Conversion to mzML", "value" => ""
              end
            end
          end
          xml.run "id" => "run_1", "defaultInstrumentConfigurationRef" => "instrument", "defaultSourceFileRef" => "_" + "#{File.basename(URI.parse(txt.url).path)}" do
            xml.spectrumList "count" => "1", "defaultDataProcessingRef" => "SpecDB_processing" do
              xml.spectrum "id" => "file=_" + "#{File.basename(URI.parse(txt.url).path)}" , "index" => "0", "defaultArrayLength" => self.peaks.count do
                xml.cvParam "cvRef" => "MS", "accession" => "MS:1000580", "name" => "MSn spectrum", "value" => ""
                xml.cvParam "cvRef" => "MS", "accession" => "MS:1000127", "name" => "centroid spectrum", "value" => ""
                if self.conditions_present?
                  case self.ionization_mode
                  when "Positive"
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000130", "name" => "positive scan", "value" => ""
                  when "Negative"
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000129", "name" => "negative scan", "value" => ""
                  end
                end
                xml.binaryDataArrayList "count"=>"2" do
                  str = self.binary_mz
                  xml.binaryDataArray "encodedLength" => str.length, "dataProcessingRef" => "SpecDB_processing" do
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000523", "name" => "64-bit float", "value" => ""
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000576", "name" => "no compression", "value" => ""
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000514", "name" => "m/z array", "value" => "", "unitCvRef"=>"MS", "unitAccession"=>"MS:1000040", "unitName"=>"m/z"
                    xml.binary str
                  end
                  str = self.binary_intensity
                  xml.binaryDataArray "encodedLength" => str.length, "dataProcessingRef" => "SpecDB_processing" do
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000523", "name" => "64-bit float", "value" => ""
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000576", "name" => "no compression", "value" => ""
                    xml.cvParam "cvRef" => "MS", "accession" => "MS:1000515", "name" => "intensity array", "value" => "", "unitCvRef"=>"MS", "unitAccession"=>"MS:1000131", "unitName"=>"number of counts"
                    xml.binary str
                  end
                end
              end
            end
          end
        end
      end
      builder.to_xml
    end
  end
end
