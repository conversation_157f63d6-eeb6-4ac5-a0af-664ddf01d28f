module Specdb
  autoload :Configurable, 'specdb/configurable'

  class Configuration < Configurable::Configuration

    # Settings that can be configured
    configurable :site, :format, :password, :user, :spectrum_name,
      :spectrum_collection, :cache_folder, :accessor_database_name,
      :compound_class, :compound_id_column, :compound_name_column,
      :compound_id_getter, :inchi_key_column, :mono_mass_column,
      :formula_method #,:cache

    # Set up default values for optional settings, settings
    # without default values will be required
    default_configuration \
      :site                   => "http://specdb.wishartlab.com/",
      :user                   => nil,
      :password               => nil,
      :format                 => :json,
      :spectrum_name          => "spectrum",
      :spectrum_collection    => "spectra",
      :compound_class         => "Compound",
      :compound_id_getter     => "to_param",
      :inchi_key_column       => "moldb_inchikey",
      :accessor_database_name => Rails.application.class.parent_name,
      :mono_mass_column       => "moldb_mono_mass",
      :formula_method         => :formula

    # override the compound_class reader so that it 
    # constantizes the string to return an actual class
    def compound_class
      @compound_class ||= setting_reader(:compound_class).classify.constantize
    end

  end
end

