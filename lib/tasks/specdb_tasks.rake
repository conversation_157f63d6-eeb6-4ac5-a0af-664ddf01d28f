namespace :specdb do
  desc "fetch assignments" 
  task :sync_assignments do |t,args|
    specdb = "<EMAIL>"
    system "rsync -avz #{specdb}:project/shared/system/ms_ms_peak_assignments/ public/system/ms_ms_peak_assignments"
    system "rsync -avz #{specdb}:project/shared/system/nmr_one_d_peak_assignments/ public/system/nmr_one_d_peak_assignments"
  end

  desc "register molecules"
  task :register_molecules => [:environment]  do |t,args|
    registration_key = "40bef59334846a04e0ce8468dc914d71ade40450b52c80846a4f73d9d18c5a2d599bb6a7e0dd7eec7eac7994644a312c20176c42f5a1b47b32a03f9ab2ddc4df"
    Specdb::BulkRegistration.register_items registration_key
  end
end
