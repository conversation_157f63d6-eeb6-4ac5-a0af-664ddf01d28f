Specdb::Engine.routes.draw do

  resources :nmr_one_d_search, except: [:edit, :update]

  # resources :nmr_one_d, only: [:index, :show] do
  #   resources :peaks,
  #     only:     :index, controller: "peaks",
  #     defaults: { spectrum_type: :nmr_one_d } do
  #       resources :assignments, only: :index, controller: "peak_assignments"
  #     end
  # end

  # All the different types of spectra have the same routes
  [:nmr_one_d, :nmr_two_d, :c_ms, :ei_ms, :ms_ms, :ms_ir].each do |spectrum|
    # Had to do this way instead of "get 'search', on: :collection"
    # because the path helper was not being generated the right way
    get  "#{spectrum}/search" => "#{spectrum}#search"
    post "#{spectrum}/search" => "#{spectrum}#search"
    get  "#{spectrum}/load_candidate" => "#{spectrum}#load_candidate"

    resources spectrum, only: [:index, :show, :generate_mzml] do
      # All the spectrums need to have their peaks and
      # peak_assignments accessible  for the spectrum viewer
      resources :peaks,
        only:     :index, controller: "peaks",
        defaults: { spectrum_type: spectrum } do
          resources :assignments, only: :index, controller: "peak_assignments"
        end
    end

  end

  # Redirect routes like: /:mounted_namespace/spectra/nmr_one_d/
  # to just /:mounted_namespace/nmr_one_d
  #
  # This fixes a duplicated namespace problem after
  # migrating to isolated namespace.
  spectra_redirect_function = redirect do |path_params,req|
    # remove the second /spectra/ portion
    req.fullpath.sub(%r{^/(.+)/spectra},'/\1')
  end
  # match paths with controllers and actions
  get "spectra", to: spectra_redirect_function
  get "spectra/:p1", to: spectra_redirect_function
  get "spectra/:p1/:p2", to: spectra_redirect_function
  get "spectra/:p1/:p2/:p3", to: spectra_redirect_function

  # get "spectra/:p1/:p2/generate_mzml", to: spectra_redirect_function
  # get "spectra/:p1/:p2/:p3/generate_mzml", to: spectra_redirect_function

  # For now we just have ms search
  # resources :ms_search, only: :search do
  # post 'ms/search/download',  to: spectra_redirect_function
  # end

  match 'ms/search' => 'ms#search', via: [:get, :post]
  match 'ms_cmm/search' => 'ms_cmm#search', via: [:get, :post]


  resources :ms_csv_download do
    collection do
      get :csv_ms_search
      post :csv_ms_search
    end
  end

  resources :mzcal do
    member do
      get :calculator
      post :calculator
    end
  end

  resources :ms_ms do
    member do
      get :generate_mzml
    end
  end
 resources :c_ms do
    member do
      get :generate_mzml
    end
  end
  resources :ms do
    collection do 
      get :generate_csv
    end
    member do
      get :generate_mzml
    end
  end
  resources :ei_ms do
    member do
      get :generate_mzml
    end
  end
  resources :statistics, only: [:show, :index]
end
