require 'her'

SPECDB_API = Her::API.new
SPECDB_API.setup url: Specdb.config.site do |c|
  # Request
  c.use Faraday::Request::UrlEncoded
  c.use Her::Middleware::AcceptJSON
  c.use Faraday::Request::BasicAuthentication,
    Specdb.config.user,
    Specdb.config.password

  # Response
  c.use Her::Middleware::DefaultParseJSON

  # Adapter
  c.use Faraday::Adapter::NetHttp

  c.options[:open_timeout] = 600
  c.options[:timeout] = 600
end
