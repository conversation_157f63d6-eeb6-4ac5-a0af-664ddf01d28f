ActiveSupport::Inflector.inflections do |inflect|
  inflect.irregular 'c_ms', 'c_ms'  
  inflect.irregular 'ei_ms', 'ei_ms'  
  inflect.irregular 'ms_ms', 'ms_ms'
  inflect.irregular 'nmr_one_d', 'nmr_one_d'
  inflect.irregular 'nmr_two_d', 'nmr_two_d'
  inflect.irregular 'MsMs', 'MsMs'
  inflect.irregular 'CMs', 'CMs'
  inflect.irregular 'EiMs', 'EiMs'
  inflect.irregular 'NmrOneD', 'NmrOneD'
  inflect.irregular 'NmrTwoD', 'NmrTwoD'
  inflect.irregular 'spectrum', 'spectra'
  inflect.irregular 'ms_ir', 'ms_ir'
  inflect.irregular 'MsIr', 'MsIr'
end
