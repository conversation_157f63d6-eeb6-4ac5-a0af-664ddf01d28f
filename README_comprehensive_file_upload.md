# Comprehensive File Upload Implementation for Specdb Search Forms

## Overview

This implementation adds file upload functionality to all major search forms in the Specdb application:

1. **MS Search** - Upload peaklist files for mass spectrometry searches
2. **MS/MS Search** - Upload peak files for tandem mass spectrometry searches  
3. **GC-MS Search** - Upload peak files for gas chromatography-mass spectrometry searches
4. **NMR Search** - Upload separate files for chemical peaks and intensities

## Features

### ✅ **Universal File Upload Support**
- **File Format**: .txt files only
- **File Size Limit**: 1MB maximum
- **Line Endings**: Supports both Windows (CRLF) and Unix (LF) line endings
- **Validation**: Client-side and server-side validation
- **Error Handling**: User-friendly error messages

### ✅ **User Interface**
- **Choose File Button**: Opens file selection dialog
- **File Name Display**: Shows selected file name
- **Clear Button**: Removes selected file and clears textarea
- **Help Text**: Explains file format requirements
- **Responsive Design**: Works on desktop and mobile devices

### ✅ **Backward Compatibility**
- **Existing Functionality**: All existing textarea input methods continue to work
- **Form Submission**: No changes to existing form submission logic
- **API Compatibility**: Server-side processing maintains existing API contracts

## Implementation Details

### Files Modified

#### Views (HTML/Slim Templates)
- `app/views/specdb/ms/search.html.slim` - MS search form
- `app/views/specdb/ms_ms/search.html.slim` - MS/MS search form  
- `app/views/specdb/c_ms/search.html.slim` - GC-MS search form
- `app/views/specdb/nmr_one_d/search/_pure_compound.html.slim` - NMR search form
- `app/views/specdb/nmr_one_d/search/new.html.slim` - NMR main form

#### Controllers (Ruby)
- `app/controllers/specdb/ms_controller.rb` - MS search processing
- `app/controllers/specdb/ms_ms_controller.rb` - MS/MS search processing
- `app/controllers/specdb/c_ms_controller.rb` - GC-MS search processing
- `app/controllers/specdb/nmr_one_d_search_controller.rb` - NMR search processing

#### JavaScript
- `app/assets/javascripts/specdb/file_upload.js` - Universal file upload handler
- `app/assets/javascripts/specdb/application.js` - Asset manifest (updated)

#### Stylesheets
- `app/assets/stylesheets/specdb/file_upload.css` - File upload styling

### Technical Architecture

#### Client-Side (JavaScript)
```javascript
// Universal file upload initialization
initializeFileUpload(fileInputId, uploadBtnId, clearBtnId, displayId, textareaId)
```

**Features:**
- Generic function handles all search forms
- File validation (type, size)
- FileReader API for content loading
- Event handling for file selection and clearing
- Integration with existing textareas

#### Server-Side (Ruby)
```ruby
# Generic file input handler
def get_peaks_input(params)
  if params[:peaklist_file].present? && params[:peaklist_file].respond_to?(:read)
    # Process uploaded file
  else
    # Use textarea input
  end
end
```

**Features:**
- File upload detection and processing
- Size validation (1MB limit)
- Fallback to textarea input
- Error handling with flash messages

### Form-Specific Implementation

#### 1. MS Search
- **File Parameter**: `peaklist_file`
- **Textarea**: `query_masses`
- **Format**: One mass per line, optional CCS value
- **Example**: `175.119 150.2`

#### 2. MS/MS Search  
- **File Parameter**: `peaklist_file`
- **Textarea**: `peaks`
- **Format**: m/z and intensity per line
- **Example**: `175.119 1000`

#### 3. GC-MS Search
- **File Parameter**: `peaklist_file` 
- **Textarea**: `peaks`
- **Format**: m/z and optional intensity per line
- **Example**: `175.119 850`

#### 4. NMR Search
- **Peaks File Parameter**: `peaks_file`
- **Peaks Textarea**: `peaks`
- **Intensities File Parameter**: `intensities_file`
- **Intensities Textarea**: `intensities`
- **Format**: One value per line
- **Example Peaks**: `7.25`
- **Example Intensities**: `100`

## Usage Instructions

### For Users

1. **Navigate** to any search form (MS, MS/MS, GC-MS, or NMR)
2. **Choose Option**:
   - **Manual Entry**: Type/paste data directly into textarea
   - **File Upload**: Click "Choose File" button and select .txt file
3. **File Requirements**:
   - Must be .txt format
   - Maximum 1MB file size
   - Follow format requirements for each search type
4. **Submit** search as normal

### File Format Examples

#### MS Search File (`sample_ms_peaklist.txt`)
```
175.119 150.2
147.044 120.5
119.049 180.3
91.054
77.039 110.8
```

#### MS/MS Search File (`sample_msms_peaklist.txt`)
```
175.119 1000
147.044 850
119.049 1200
91.054 650
```

#### NMR Peaks File (`sample_nmr_peaks.txt`)
```
7.25
7.18
6.95
6.88
```

#### NMR Intensities File (`sample_nmr_intensities.txt`)
```
100
95
80
85
```

## Testing

### Test Files Provided
- `test_comprehensive_file_upload.html` - Complete functionality test
- `sample_ms_peaklist.txt` - MS search test data
- `sample_msms_peaklist.txt` - MS/MS search test data  
- `sample_nmr_peaks.txt` - NMR peaks test data
- `sample_nmr_intensities.txt` - NMR intensities test data

### Manual Testing Steps
1. Open test HTML file in browser
2. Test each search form section
3. Upload sample files and verify content loads
4. Test clear functionality
5. Test manual typing after file upload

## Error Handling

### Client-Side Validation
- File type validation (.txt only)
- File size validation (1MB limit)
- Content format validation (basic)

### Server-Side Validation  
- File size validation
- Content processing with error handling
- Flash message display for errors
- Graceful fallback to textarea input

## Browser Compatibility

- **Modern Browsers**: Full support (Chrome, Firefox, Safari, Edge)
- **FileReader API**: Required for file content reading
- **jQuery**: Used for DOM manipulation and event handling
- **Bootstrap**: Used for responsive styling

## Future Enhancements

### Potential Improvements
- **Drag & Drop**: Add drag-and-drop file upload
- **Multiple Files**: Support multiple file selection
- **File Preview**: Show file content preview before upload
- **Format Detection**: Auto-detect file format
- **Progress Indicators**: Show upload progress for large files
- **File History**: Remember recently uploaded files

### Additional File Formats
- **CSV Support**: Add comma-separated value support
- **Excel Support**: Add .xlsx file support  
- **JSON Support**: Add structured data format support

## Maintenance Notes

### Code Organization
- **Modular Design**: Generic functions handle multiple forms
- **Consistent Naming**: Predictable ID patterns across forms
- **Error Handling**: Comprehensive validation and user feedback
- **Documentation**: Inline comments and external documentation

### Performance Considerations
- **File Size Limits**: Prevent server overload
- **Client-Side Processing**: Reduce server load where possible
- **Efficient DOM Manipulation**: Minimize jQuery operations
- **Memory Management**: Proper cleanup of FileReader objects

This implementation provides a robust, user-friendly file upload system that enhances the existing search functionality while maintaining full backward compatibility.
