<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive File Upload Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .test-section {
            border: 1px solid #ccc;
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        textarea {
            width: 100%;
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 8px 12px;
        }
        .file-info {
            margin-left: 10px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Comprehensive File Upload Test</h1>
    
    <!-- MS Search Test -->
    <div class="test-section">
        <h3>MS Search</h3>
        <textarea id="query_masses" rows="5" placeholder="MS masses will appear here..."></textarea>
        <br>
        <input type="file" id="peaklist-file-input" accept=".txt" style="display: none;">
        <button type="button" id="file-upload-btn" onclick="document.getElementById('peaklist-file-input').click();">Choose File</button>
        <span id="file-name-display" class="file-info"></span>
        <button type="button" id="clear-file-btn" style="display: none;">Clear</button>
    </div>
    
    <!-- MS/MS Search Test -->
    <div class="test-section">
        <h3>MS/MS Search</h3>
        <textarea id="peaks" rows="5" placeholder="MS/MS peaks will appear here..."></textarea>
        <br>
        <input type="file" id="msms-peaklist-file-input" accept=".txt" style="display: none;">
        <button type="button" id="msms-file-upload-btn" onclick="document.getElementById('msms-peaklist-file-input').click();">Choose File</button>
        <span id="msms-file-name-display" class="file-info"></span>
        <button type="button" id="msms-clear-file-btn" style="display: none;">Clear</button>
    </div>
    
    <!-- GC-MS Search Test -->
    <div class="test-section">
        <h3>GC-MS Search</h3>
        <textarea id="gcms-peaks" rows="5" placeholder="GC-MS peaks will appear here..."></textarea>
        <br>
        <input type="file" id="gcms-peaklist-file-input" accept=".txt" style="display: none;">
        <button type="button" id="gcms-file-upload-btn" onclick="document.getElementById('gcms-peaklist-file-input').click();">Choose File</button>
        <span id="gcms-file-name-display" class="file-info"></span>
        <button type="button" id="gcms-clear-file-btn" style="display: none;">Clear</button>
    </div>
    
    <!-- NMR Search Test -->
    <div class="test-section">
        <h3>NMR Search - Peaks</h3>
        <textarea id="nmr-peaks" rows="5" placeholder="NMR peaks will appear here..."></textarea>
        <br>
        <input type="file" id="nmr-peaks-file-input" accept=".txt" style="display: none;">
        <button type="button" id="nmr-peaks-file-upload-btn" onclick="document.getElementById('nmr-peaks-file-input').click();">Choose Peaks File</button>
        <span id="nmr-peaks-file-name-display" class="file-info"></span>
        <button type="button" id="nmr-peaks-clear-file-btn" style="display: none;">Clear</button>
    </div>
    
    <div class="test-section">
        <h3>NMR Search - Intensities</h3>
        <textarea id="intensities" rows="5" placeholder="NMR intensities will appear here..."></textarea>
        <br>
        <input type="file" id="nmr-intensities-file-input" accept=".txt" style="display: none;">
        <button type="button" id="nmr-intensities-file-upload-btn" onclick="document.getElementById('nmr-intensities-file-input').click();">Choose Intensities File</button>
        <span id="nmr-intensities-file-name-display" class="file-info"></span>
        <button type="button" id="nmr-intensities-clear-file-btn" style="display: none;">Clear</button>
    </div>
    
    <script>
        $(document).ready(function() {
            console.log('Document ready - initializing file uploads');
            
            // Initialize all file upload handlers
            initializeFileUpload('peaklist-file-input', 'file-upload-btn', 'clear-file-btn', 'file-name-display', 'query_masses');
            initializeFileUpload('msms-peaklist-file-input', 'msms-file-upload-btn', 'msms-clear-file-btn', 'msms-file-name-display', 'peaks');
            initializeFileUpload('gcms-peaklist-file-input', 'gcms-file-upload-btn', 'gcms-clear-file-btn', 'gcms-file-name-display', 'gcms-peaks');
            initializeFileUpload('nmr-peaks-file-input', 'nmr-peaks-file-upload-btn', 'nmr-peaks-clear-file-btn', 'nmr-peaks-file-name-display', 'nmr-peaks');
            initializeFileUpload('nmr-intensities-file-input', 'nmr-intensities-file-upload-btn', 'nmr-intensities-clear-file-btn', 'nmr-intensities-file-name-display', 'intensities');
        });
        
        function initializeFileUpload(fileInputId, uploadBtnId, clearBtnId, displayId, textareaId) {
            var fileInput = $('#' + fileInputId);
            var fileUploadBtn = $('#' + uploadBtnId);
            var clearFileBtn = $('#' + clearBtnId);
            var fileNameDisplay = $('#' + displayId);
            var textarea = $('#' + textareaId);
            
            console.log('Initializing file upload for:', fileInputId);
            
            // Handle file selection
            fileInput.on('change', function(evt) {
                console.log('File input changed for:', fileInputId);
                var file = evt.target.files[0];
                if (file) {
                    console.log('File selected:', file.name);
                    
                    // Simple validation
                    if (!file.name.toLowerCase().endsWith('.txt')) {
                        alert('Please select a .txt file');
                        fileInput.val('');
                        return;
                    }

                    if (file.size > 1024 * 1024) {
                        alert('File size must be less than 1MB');
                        fileInput.val('');
                        return;
                    }

                    // Display file name and show clear button
                    fileNameDisplay.text(file.name);
                    clearFileBtn.show();

                    // Read and load file content
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var content = e.target.result;
                        console.log('File content loaded, length:', content.length);
                        textarea.val(content.trim());
                        console.log('File content loaded into textarea');
                    };
                    
                    reader.onerror = function() {
                        alert('Error reading file');
                        clearFile();
                    };

                    reader.readAsText(file);
                }
            });
            
            // Handle clear button
            clearFileBtn.on('click', function() {
                console.log('Clear button clicked for:', fileInputId);
                clearFile();
            });
            
            // Clear file function
            function clearFile() {
                fileInput.val('');
                fileNameDisplay.text('');
                clearFileBtn.hide();
                textarea.val('');
            }
            
            // Handle manual textarea changes
            textarea.on('input', function() {
                if ($(this).val().length > 0 && fileNameDisplay.text().length > 0) {
                    // User is typing, clear file upload display but keep textarea content
                    fileInput.val('');
                    fileNameDisplay.text('');
                    clearFileBtn.hide();
                }
            });
        }
    </script>
</body>
</html>
