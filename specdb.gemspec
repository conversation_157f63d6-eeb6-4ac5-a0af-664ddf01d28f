# -*- encoding: utf-8 -*-
# stub: specdb 4.18.3 ruby lib

Gem::Specification.new do |s|
  s.name = "specdb".freeze
  s.version = "4.18.3"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2025-09-12"
  s.description = "Use this gem to access SpecDB. It is implemented as a rails engine.".freeze
  s.email = ["<EMAIL>".freeze]
  s.files = ["MIT-LICENSE".freeze, "README.md".freeze, "Rakefile".freeze, "app/assets/images/back_disabled.png".freeze, "app/assets/images/back_enabled.png".freeze, "app/assets/images/back_enabled_hover.png".freeze, "app/assets/images/forward_disabled.png".freeze, "app/assets/images/forward_enabled.png".freeze, "app/assets/images/forward_enabled_hover.png".freeze, "app/assets/images/header-bg.gif".freeze, "app/assets/images/loading-line.gif".freeze, "app/assets/images/loading.gif".freeze, "app/assets/images/sort_asc.png".freeze, "app/assets/images/sort_asc_disabled.png".freeze, "app/assets/images/sort_both.png".freeze, "app/assets/images/sort_desc.png".freeze, "app/assets/images/sort_desc_disabled.png".freeze, "app/assets/images/specdb".freeze, "app/assets/images/specdb/apple_logo.svg".freeze, "app/assets/images/specdb/monotone_arrow_left.png".freeze, "app/assets/images/specdb/monotone_arrow_right.png".freeze, "app/assets/images/specdb/windows_logo.svg".freeze, "app/assets/javascripts/specdb".freeze, "app/assets/javascripts/specdb/Rickshaw.Graph.Renderer.LineBar.js".freeze, "app/assets/javascripts/specdb/application.js".freeze, "app/assets/javascripts/specdb/d3.min.js".freeze, "app/assets/javascripts/specdb/d3.v3.min.js".freeze, "app/assets/javascripts/specdb/jspectra_viewer.js".freeze, "app/assets/javascripts/specdb/loader.coffee".freeze, "app/assets/javascripts/specdb/loader_submit.js".freeze, "app/assets/javascripts/specdb/ms_ir.coffee".freeze, "app/assets/javascripts/specdb/ms_search.coffee".freeze, "app/assets/javascripts/specdb/mz_plot.js".freeze, "app/assets/javascripts/specdb/mz_plot_one_d.js".freeze, "app/assets/javascripts/specdb/nmr_one_d.coffee".freeze, "app/assets/javascripts/specdb/nmr_two_d.coffee".freeze, "app/assets/javascripts/specdb/rickshaw.min.js".freeze, "app/assets/javascripts/specdb/search.coffee".freeze, "app/assets/javascripts/specdb/spectra.coffee".freeze, "app/assets/javascripts/specdb/xml2json.min.js".freeze, "app/assets/stylesheets/specdb".freeze, "app/assets/stylesheets/specdb/graph.scss".freeze, "app/assets/stylesheets/specdb/jspectra_viewer.css".freeze, "app/assets/stylesheets/specdb/nmrml.scss".freeze, "app/assets/stylesheets/specdb/rickshaw.css".freeze, "app/assets/stylesheets/specdb/search.scss".freeze, "app/assets/stylesheets/specdb/specdb.scss".freeze, "app/assets/stylesheets/specdb/spectra.scss".freeze, "app/controllers/specdb".freeze, "app/controllers/specdb/c_ms_controller.rb".freeze, "app/controllers/specdb/ei_ms_controller.rb".freeze, "app/controllers/specdb/ms_cmm_controller.rb".freeze, "app/controllers/specdb/ms_controller.rb".freeze, "app/controllers/specdb/ms_ir_controller.rb".freeze, "app/controllers/specdb/ms_ms_controller.rb".freeze, "app/controllers/specdb/mzcal_controller.rb".freeze, "app/controllers/specdb/nmr".freeze, "app/controllers/specdb/nmr/one_d".freeze, "app/controllers/specdb/nmr/one_d/search_controller.rb".freeze, "app/controllers/specdb/nmr_one_d_controller.rb".freeze, "app/controllers/specdb/nmr_one_d_search_controller.rb".freeze, "app/controllers/specdb/nmr_two_d_controller.rb".freeze, "app/controllers/specdb/peak_assignments_controller.rb".freeze, "app/controllers/specdb/peaks_controller.rb".freeze, "app/controllers/specdb/specdb_controller.rb".freeze, "app/controllers/specdb/spectra_collection_controller.rb".freeze, "app/controllers/specdb/spectra_controller.rb".freeze, "app/helpers/specdb".freeze, "app/helpers/specdb/application_helper.rb".freeze, "app/helpers/specdb/main_app_helper.rb".freeze, "app/helpers/specdb/routes_helper.rb".freeze, "app/helpers/specdb/search_helper.rb".freeze, "app/helpers/specdb/specdb_config_helper.rb".freeze, "app/models/specdb".freeze, "app/models/specdb/assignment.rb".freeze, "app/models/specdb/bulk_registration.rb".freeze, "app/models/specdb/c_ms.rb".freeze, "app/models/specdb/c_ms_result.rb".freeze, "app/models/specdb/compound_decorator.rb".freeze, "app/models/specdb/ei_ms.rb".freeze, "app/models/specdb/mode.rb".freeze, "app/models/specdb/ms_cmm_search.rb".freeze, "app/models/specdb/ms_csv.rb".freeze, "app/models/specdb/ms_ir.rb".freeze, "app/models/specdb/ms_ir_result.rb".freeze, "app/models/specdb/ms_ms.rb".freeze, "app/models/specdb/ms_ms_result.rb".freeze, "app/models/specdb/ms_search.rb".freeze, "app/models/specdb/mzcal.rb".freeze, "app/models/specdb/nmr_one_d.rb".freeze, "app/models/specdb/nmr_one_d_result.rb".freeze, "app/models/specdb/nmr_one_d_search.rb".freeze, "app/models/specdb/nmr_two_d.rb".freeze, "app/models/specdb/nmr_two_d_result.rb".freeze, "app/models/specdb/peak.rb".freeze, "app/models/specdb/specdb_resource.rb".freeze, "app/models/specdb/spectrum.rb".freeze, "app/models/specdb/spectrum_collection.rb".freeze, "app/models/specdb/spectrum_model.rb".freeze, "app/models/specdb/spectrum_search_result.rb".freeze, "app/models/specdb/statistic.rb".freeze, "app/views/specdb".freeze, "app/views/specdb/_cmm_search_link.html.slim".freeze, "app/views/specdb/_navigation_search_links.html.slim".freeze, "app/views/specdb/_nmr_navigation_search_links.html.erb".freeze, "app/views/specdb/_spectra_list.html.slim".freeze, "app/views/specdb/_spectra_list_data.html.slim".freeze, "app/views/specdb/_spectra_list_data_c_ms.html.slim".freeze, "app/views/specdb/_spectra_list_data_index.html.slim".freeze, "app/views/specdb/_spectra_list_data_nmr.html.slim".freeze, "app/views/specdb/_spectra_list_index.html.slim".freeze, "app/views/specdb/_spectra_list_separate.html.slim".freeze, "app/views/specdb/c_ms".freeze, "app/views/specdb/c_ms/_results.html.slim".freeze, "app/views/specdb/c_ms/search.html.slim".freeze, "app/views/specdb/c_ms/show.html.slim".freeze, "app/views/specdb/ei_ms".freeze, "app/views/specdb/ei_ms/show.html.slim".freeze, "app/views/specdb/error_connecting.html.slim".freeze, "app/views/specdb/errors".freeze, "app/views/specdb/errors/404.html.erb".freeze, "app/views/specdb/ms".freeze, "app/views/specdb/ms/_results.html.slim".freeze, "app/views/specdb/ms/search.html.slim".freeze, "app/views/specdb/ms/search.txt.erb".freeze, "app/views/specdb/ms_cmm".freeze, "app/views/specdb/ms_cmm/_cmm_note.html.slim".freeze, "app/views/specdb/ms_cmm/_results.html.slim".freeze, "app/views/specdb/ms_cmm/search.html.slim".freeze, "app/views/specdb/ms_ir".freeze, "app/views/specdb/ms_ir/_spectra_viewer_ir.html.slim".freeze, "app/views/specdb/ms_ir/show.html.slim".freeze, "app/views/specdb/ms_ms".freeze, "app/views/specdb/ms_ms/_results.html.slim".freeze, "app/views/specdb/ms_ms/search.html.slim".freeze, "app/views/specdb/ms_ms/show.html.slim".freeze, "app/views/specdb/mzcal".freeze, "app/views/specdb/mzcal/_calculator_details_modal.html.slim".freeze, "app/views/specdb/mzcal/calculator.html.slim".freeze, "app/views/specdb/nmr_one_d".freeze, "app/views/specdb/nmr_one_d/_spectra_viewer.html.slim".freeze, "app/views/specdb/nmr_one_d/search".freeze, "app/views/specdb/nmr_one_d/search/_advanced_search.html.slim".freeze, "app/views/specdb/nmr_one_d/search/_mixture.html.slim".freeze, "app/views/specdb/nmr_one_d/search/_pure_compound.html.slim".freeze, "app/views/specdb/nmr_one_d/search/_results.html.slim".freeze, "app/views/specdb/nmr_one_d/search/_search_logic.html.erb".freeze, "app/views/specdb/nmr_one_d/search/new.html.slim".freeze, "app/views/specdb/nmr_one_d/search/show.html.slim".freeze, "app/views/specdb/nmr_one_d/show.html.slim".freeze, "app/views/specdb/nmr_two_d".freeze, "app/views/specdb/nmr_two_d/_results.html.slim".freeze, "app/views/specdb/nmr_two_d/_spectra_viewer_2d.html.slim".freeze, "app/views/specdb/nmr_two_d/search.html.slim".freeze, "app/views/specdb/nmr_two_d/show.html.slim".freeze, "app/views/specdb/search".freeze, "app/views/specdb/search/_navigation.html.slim".freeze, "app/views/specdb/search/_nmr_navigation.html.slim".freeze, "app/views/specdb/search/examples".freeze, "app/views/specdb/search/examples/_c_ms.json".freeze, "app/views/specdb/search/examples/_c_ms_2.json".freeze, "app/views/specdb/search/examples/_ms.json".freeze, "app/views/specdb/search/examples/_ms_ccs.json".freeze, "app/views/specdb/search/examples/_ms_cmm.json".freeze, "app/views/specdb/search/examples/_ms_ms.json".freeze, "app/views/specdb/search/examples/_ms_ms_ccs.json".freeze, "app/views/specdb/search/examples/_nmr_one_d.json".freeze, "app/views/specdb/search/examples/_nmr_two_d.json".freeze, "config/initializers/her.rb".freeze, "config/initializers/inflections.rb".freeze, "config/initializers/mime_types.rb".freeze, "config/initializers/renderers.rb".freeze, "config/routes.rb".freeze, "lib/generators".freeze, "lib/generators/specdb".freeze, "lib/generators/specdb/config".freeze, "lib/generators/specdb/config/USAGE".freeze, "lib/generators/specdb/config/config_generator.rb".freeze, "lib/generators/specdb/config/templates".freeze, "lib/generators/specdb/config/templates/specdb.yml.erb".freeze, "lib/specdb".freeze, "lib/specdb.rb".freeze, "lib/specdb/configurable".freeze, "lib/specdb/configurable.rb".freeze, "lib/specdb/configurable/configuration.rb".freeze, "lib/specdb/configuration.rb".freeze, "lib/specdb/engine.rb".freeze, "lib/specdb/exceptions.rb".freeze, "lib/specdb/has_compound.rb".freeze, "lib/specdb/has_spectra.rb".freeze, "lib/specdb/mz_ml.rb".freeze, "lib/specdb/rescue_helper.rb".freeze, "lib/specdb/version.rb".freeze, "lib/tasks/specdb_tasks.rake".freeze, "test/dummy".freeze, "test/dummy/README.rdoc".freeze, "test/dummy/Rakefile".freeze, "test/dummy/app".freeze, "test/dummy/app/assets".freeze, "test/dummy/app/assets/javascripts".freeze, "test/dummy/app/assets/javascripts/application.js".freeze, "test/dummy/app/assets/javascripts/metabolites.js".freeze, "test/dummy/app/assets/stylesheets".freeze, "test/dummy/app/assets/stylesheets/application.css".freeze, "test/dummy/app/assets/stylesheets/metabolites.css".freeze, "test/dummy/app/assets/stylesheets/scaffold.css".freeze, "test/dummy/app/controllers".freeze, "test/dummy/app/controllers/application_controller.rb".freeze, "test/dummy/app/controllers/metabolites_controller.rb".freeze, "test/dummy/app/helpers".freeze, "test/dummy/app/helpers/application_helper.rb".freeze, "test/dummy/app/helpers/metabolites_helper.rb".freeze, "test/dummy/app/mailers".freeze, "test/dummy/app/models".freeze, "test/dummy/app/models/metabolite.rb".freeze, "test/dummy/app/views".freeze, "test/dummy/app/views/layouts".freeze, "test/dummy/app/views/layouts/application.html.erb".freeze, "test/dummy/app/views/metabolites".freeze, "test/dummy/app/views/metabolites/_form.html.erb".freeze, "test/dummy/app/views/metabolites/edit.html.erb".freeze, "test/dummy/app/views/metabolites/index.html.erb".freeze, "test/dummy/app/views/metabolites/new.html.erb".freeze, "test/dummy/app/views/metabolites/show.html.erb".freeze, "test/dummy/config".freeze, "test/dummy/config.ru".freeze, "test/dummy/config/application.rb".freeze, "test/dummy/config/boot.rb".freeze, "test/dummy/config/database.yml".freeze, "test/dummy/config/environment.rb".freeze, "test/dummy/config/environments".freeze, "test/dummy/config/environments/development.rb".freeze, "test/dummy/config/environments/production.rb".freeze, "test/dummy/config/environments/test.rb".freeze, "test/dummy/config/initializers".freeze, "test/dummy/config/initializers/backtrace_silencers.rb".freeze, "test/dummy/config/initializers/inflections.rb".freeze, "test/dummy/config/initializers/mime_types.rb".freeze, "test/dummy/config/initializers/secret_token.rb".freeze, "test/dummy/config/initializers/session_store.rb".freeze, "test/dummy/config/initializers/wrap_parameters.rb".freeze, "test/dummy/config/locales".freeze, "test/dummy/config/locales/en.yml".freeze, "test/dummy/config/routes.rb".freeze, "test/dummy/config/specdb.yml".freeze, "test/dummy/db".freeze, "test/dummy/db/migrate".freeze, "test/dummy/db/migrate/20120711233436_create_metabolites.rb".freeze, "test/dummy/db/schema.rb".freeze, "test/dummy/lib".freeze, "test/dummy/lib/assets".freeze, "test/dummy/log".freeze, "test/dummy/public".freeze, "test/dummy/public/404.html".freeze, "test/dummy/public/422.html".freeze, "test/dummy/public/500.html".freeze, "test/dummy/public/favicon.ico".freeze, "test/dummy/script".freeze, "test/dummy/script/rails".freeze, "test/dummy/test".freeze, "test/dummy/test/functional".freeze, "test/dummy/test/functional/metabolites_controller_test.rb".freeze, "test/dummy/test/unit".freeze, "test/dummy/test/unit/helpers".freeze, "test/dummy/test/unit/helpers/metabolites_helper_test.rb".freeze, "test/dummy/test/unit/metabolite.rb".freeze, "test/dummy/test/unit/metabolite_test.rb".freeze, "test/fixtures/metabolites.yml".freeze, "test/fixtures/specdb".freeze, "test/fixtures/specdb/ms_ms.yml".freeze, "test/integration/navigation_test.rb".freeze, "test/specdb_test.rb".freeze, "test/test_helper.rb".freeze, "test/unit".freeze, "test/unit/specdb".freeze, "test/unit/specdb/application_helper_test.rb".freeze, "test/unit/specdb/has_spectrum_test.rb".freeze, "test/unit/specdb/spectrum_collection_test.rb".freeze, "test/unit/specdb/spectrum_test.rb".freeze]
  s.homepage = "http://specdb.wishartlab.com".freeze
  s.rubygems_version = "3.1.2".freeze
  s.summary = "Interface to SpecDB".freeze
  s.test_files = ["test/unit".freeze, "test/unit/specdb".freeze, "test/unit/specdb/application_helper_test.rb".freeze, "test/unit/specdb/has_spectrum_test.rb".freeze, "test/unit/specdb/spectrum_collection_test.rb".freeze, "test/unit/specdb/spectrum_test.rb".freeze, "test/dummy".freeze, "test/dummy/app".freeze, "test/dummy/app/mailers".freeze, "test/dummy/app/models".freeze, "test/dummy/app/models/metabolite.rb".freeze, "test/dummy/app/controllers".freeze, "test/dummy/app/controllers/application_controller.rb".freeze, "test/dummy/app/controllers/metabolites_controller.rb".freeze, "test/dummy/app/views".freeze, "test/dummy/app/views/metabolites".freeze, "test/dummy/app/views/metabolites/index.html.erb".freeze, "test/dummy/app/views/metabolites/edit.html.erb".freeze, "test/dummy/app/views/metabolites/show.html.erb".freeze, "test/dummy/app/views/metabolites/_form.html.erb".freeze, "test/dummy/app/views/metabolites/new.html.erb".freeze, "test/dummy/app/views/layouts".freeze, "test/dummy/app/views/layouts/application.html.erb".freeze, "test/dummy/app/assets".freeze, "test/dummy/app/assets/javascripts".freeze, "test/dummy/app/assets/javascripts/metabolites.js".freeze, "test/dummy/app/assets/javascripts/application.js".freeze, "test/dummy/app/assets/stylesheets".freeze, "test/dummy/app/assets/stylesheets/application.css".freeze, "test/dummy/app/assets/stylesheets/scaffold.css".freeze, "test/dummy/app/assets/stylesheets/metabolites.css".freeze, "test/dummy/app/helpers".freeze, "test/dummy/app/helpers/metabolites_helper.rb".freeze, "test/dummy/app/helpers/application_helper.rb".freeze, "test/dummy/test".freeze, "test/dummy/test/unit".freeze, "test/dummy/test/unit/metabolite_test.rb".freeze, "test/dummy/test/unit/helpers".freeze, "test/dummy/test/unit/helpers/metabolites_helper_test.rb".freeze, "test/dummy/test/unit/metabolite.rb".freeze, "test/dummy/test/functional".freeze, "test/dummy/test/functional/metabolites_controller_test.rb".freeze, "test/dummy/config".freeze, "test/dummy/config/routes.rb".freeze, "test/dummy/config/locales".freeze, "test/dummy/config/locales/en.yml".freeze, "test/dummy/config/environments".freeze, "test/dummy/config/environments/production.rb".freeze, "test/dummy/config/environments/development.rb".freeze, "test/dummy/config/environments/test.rb".freeze, "test/dummy/config/environment.rb".freeze, "test/dummy/config/application.rb".freeze, "test/dummy/config/database.yml".freeze, "test/dummy/config/boot.rb".freeze, "test/dummy/config/specdb.yml".freeze, "test/dummy/config/initializers".freeze, "test/dummy/config/initializers/backtrace_silencers.rb".freeze, "test/dummy/config/initializers/mime_types.rb".freeze, "test/dummy/config/initializers/session_store.rb".freeze, "test/dummy/config/initializers/wrap_parameters.rb".freeze, "test/dummy/config/initializers/secret_token.rb".freeze, "test/dummy/config/initializers/inflections.rb".freeze, "test/dummy/config.ru".freeze, "test/dummy/script".freeze, "test/dummy/script/rails".freeze, "test/dummy/Rakefile".freeze, "test/dummy/public".freeze, "test/dummy/public/favicon.ico".freeze, "test/dummy/public/422.html".freeze, "test/dummy/public/500.html".freeze, "test/dummy/public/404.html".freeze, "test/dummy/lib".freeze, "test/dummy/lib/assets".freeze, "test/dummy/db".freeze, "test/dummy/db/schema.rb".freeze, "test/dummy/db/migrate".freeze, "test/dummy/db/migrate/20120711233436_create_metabolites.rb".freeze, "test/dummy/log".freeze, "test/dummy/README.rdoc".freeze, "test/specdb_test.rb".freeze, "test/integration/navigation_test.rb".freeze, "test/fixtures/specdb".freeze, "test/fixtures/specdb/ms_ms.yml".freeze, "test/fixtures/metabolites.yml".freeze, "test/test_helper.rb".freeze]

  s.installed_by_version = "3.1.2" if s.respond_to? :installed_by_version

  if s.respond_to? :specification_version then
    s.specification_version = 4
  end

  if s.respond_to? :add_runtime_dependency then
    s.add_runtime_dependency(%q<wishart>.freeze, [">= 3.5.0"])
    s.add_runtime_dependency(%q<faraday>.freeze, [">= 0"])
    s.add_runtime_dependency(%q<activeresource>.freeze, [">= 0"])
    s.add_runtime_dependency(%q<spectrum_hash>.freeze, [">= 0"])
    s.add_runtime_dependency(%q<jquery-ui-rails>.freeze, [">= 0"])
    s.add_development_dependency(%q<sqlite3>.freeze, [">= 0"])
  else
    s.add_dependency(%q<wishart>.freeze, [">= 3.5.0"])
    s.add_dependency(%q<faraday>.freeze, [">= 0"])
    s.add_dependency(%q<activeresource>.freeze, [">= 0"])
    s.add_dependency(%q<spectrum_hash>.freeze, [">= 0"])
    s.add_dependency(%q<jquery-ui-rails>.freeze, [">= 0"])
    s.add_dependency(%q<sqlite3>.freeze, [">= 0"])
  end
end
