source "http://rubygems.org"

# Declare your gem's dependencies in specdbi.gemspec.
# <PERSON><PERSON><PERSON> will treat runtime dependencies like base dependencies, and
# development dependencies will be added by default to the :development group.
gemspec

# jquery-rails is used by the dummy application
gem "jquery-rails"
gem "bootstrap-sass"

# Declare any dependencies that are still in development here instead of in
# your gemspec. These might include edge Rails or gems from your path or
# Git. Remember to move these dependencies to your gemspec before releasing
# your gem to rubygems.org.

# To use debugger
# gem 'debugger'

group :guardtest do
	gem 'guard-minitest'
	gem 'rb-fsevent', :require => RUBY_PLATFORM.include?('darwin') && 'rb-fsevent'
	gem 'growl',      :require => RUBY_PLATFORM.include?('darwin') && 'growl'
	gem 'rb-inotify', :require => RUBY_PLATFORM.include?('linux') && 'rb-inotify'
end

gem 'wishart', git: '*****************:wishartlab/wishart.git'
