<!DOCTYPE html>
<html>
<head>
    <title>File Upload Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>File Upload Test</h1>
    
    <textarea id="query_masses" rows="10" cols="50" placeholder="Content will appear here..."></textarea>
    <br><br>
    
    <input type="file" id="peaklist-file-input" accept=".txt" style="display: none;">
    <button type="button" id="file-upload-btn" onclick="alert('Button clicked!'); document.getElementById('peaklist-file-input').click();">Choose File</button>
    <span id="file-name-display" style="margin-left: 10px; font-style: italic; color: #666;"></span>
    <button type="button" id="clear-file-btn" style="display: none;">Clear</button>
    
    <script>
        $(document).ready(function() {
            console.log('Document ready');
            
            var fileInput = $('#peaklist-file-input');
            var fileNameDisplay = $('#file-name-display');
            var queryMassesTextarea = $('#query_masses');
            var clearFileBtn = $('#clear-file-btn');
            
            // Handle file selection
            fileInput.on('change', function(evt) {
                console.log('File input changed');
                var file = evt.target.files[0];
                if (file) {
                    console.log('File selected:', file.name);
                    
                    // Display file name and show clear button
                    fileNameDisplay.text(file.name);
                    clearFileBtn.show();

                    // Read and load file content
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var content = e.target.result;
                        console.log('File content loaded, length:', content.length);
                        queryMassesTextarea.val(content.trim());
                    };
                    
                    reader.readAsText(file);
                }
            });
            
            // Handle clear button
            clearFileBtn.on('click', function() {
                fileInput.val('');
                fileNameDisplay.text('');
                clearFileBtn.hide();
                queryMassesTextarea.val('');
            });
        });
    </script>
</body>
</html>
