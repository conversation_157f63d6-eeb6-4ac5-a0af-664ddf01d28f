require 'test_helper'

class MetaboliteTest < ActiveSupport::TestCase
  def setup
    @collection_name    = 'test_nmr'
    @spectra_collection = 'spectra'

    # attributes for our test spectrum
    @inchi = '1/C7H11N3O2/c1-10-3-5(9-4-10)2-6(8)7(11)12/h3-4,6H,2,8H2,1H3,(H,11,12)/t6-/m0/s1'
    @test_spectrum_id = 1
    @peak_list = [1,2,3,4,5,6,7,8,9]
    @test_spectrum = { :id => @test_spectrum_id, :inchi => @inchi , :peak_list => @peak_list }

    @test_msms = {:id => 1, :inchi => '1/C7H11N3O2/c1-10-3-5', :peak_list => [3,6,4,6]}
    @spectrum_collection =  {:test_ms_ms => [@test_msms], :test_nmr => [@test_spectrum]}

    # When the request is submitted the inchi will be escaped
    @inchi_escaped = CGI.escape @inchi

    # Mock out the request reponder for ActiveResource so we don't have to rely
    # on the actual server
    ActiveResource::HttpMock.respond_to do |mock|
      # Routes for getting single resources
      mock.post   "/#@collection_name.json", {}, @test_spectrum.to_json, 201, "Location" => "/test_spectrums/1.json"
      mock.get    "/#@test_spectrum_id.json", {}, @test_spectrum.to_json
      mock.put    "/#@test_spectrum_id.json", {}, nil, 204
      mock.delete "/#@test_spectrum_id.json", {}, nil, 200

      # Routes for getting collection of one type of spectra by inchi key
      mock.get "/#@collection_name.json?inchi=#@inchi_escaped", {}, [@test_spectrum].to_json

      # Routes for getting collection of all spectra by inchi key
      mock.get "/#@spectra_collection.json?inchi=#@inchi_escaped", {}, @spectrum_collection.to_json
    end
  end

  
end
