require 'test_helper'

class MetabolitesControllerTest < ActionController::TestCase
  fixtures :metabolites

  setup do
    @metabolite = metabolites(:one)
  end

  test "should get index" do
    get :index
    assert_response :success
    assert_not_nil assigns(:metabolites)
  end

  test "should get new" do
    get :new
    assert_response :success
  end

  test "should create metabolite" do
    assert_difference('Metabolite.count') do
      post :create, metabolite: { inchikey: @metabolite.inchi_key, name: @metabolite.name }
    end

    assert_redirected_to metabolite_path(assigns(:metabolite))
  end

  test "should show metabolite" do
    get :show, id: @metabolite
    assert_response :success
  end

  test "should get edit" do
    get :edit, id: @metabolite
    assert_response :success
  end

  test "should update metabolite" do
    put :update, id: @metabolite, metabolite: { inchi: @metabolite.inchi, name: @metabolite.name }
    assert_redirected_to metabolite_path(assigns(:metabolite))
  end

  test "should destroy metabolite" do
    assert_difference('Metabolite.count', -1) do
      delete :destroy, id: @metabolite
    end

    assert_redirected_to metabolites_path
  end
end
