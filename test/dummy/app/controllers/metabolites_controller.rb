class MetabolitesController < ApplicationController
  # GET /metabolites
  # GET /metabolites.json
  def index
    @metabolites = Metabolite.all

    respond_to do |format|
      format.html # index.html.erb
      format.json { render json: @metabolites }
    end
  end

  # GET /metabolites/1
  # GET /metabolites/1.json
  def show
    @metabolite = Metabolite.find(params[:id])

    respond_to do |format|
      format.html # show.html.erb
      format.json { render json: @metabolite }
    end
  end

  # GET /metabolites/new
  # GET /metabolites/new.json
  def new
    @metabolite = Metabolite.new

    respond_to do |format|
      format.html # new.html.erb
      format.json { render json: @metabolite }
    end
  end

  # GET /metabolites/1/edit
  def edit
    @metabolite = Metabolite.find(params[:id])
  end

  # POST /metabolites
  # POST /metabolites.json
  def create
    @metabolite = Metabolite.new(params[:metabolite])

    respond_to do |format|
      if @metabolite.save
        format.html { redirect_to @metabolite, notice: 'Metabolite was successfully created.' }
        format.json { render json: @metabolite, status: :created, location: @metabolite }
      else
        format.html { render action: "new" }
        format.json { render json: @metabolite.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /metabolites/1
  # PUT /metabolites/1.json
  def update
    @metabolite = Metabolite.find(params[:id])

    respond_to do |format|
      if @metabolite.update_attributes(params[:metabolite])
        format.html { redirect_to @metabolite, notice: 'Metabolite was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: "edit" }
        format.json { render json: @metabolite.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /metabolites/1
  # DELETE /metabolites/1.json
  def destroy
    @metabolite = Metabolite.find(params[:id])
    @metabolite.destroy

    respond_to do |format|
      format.html { redirect_to metabolites_url }
      format.json { head :no_content }
    end
  end
end
