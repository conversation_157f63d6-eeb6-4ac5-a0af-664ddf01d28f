<%= form_for(@metabolite) do |f| %>
  <% if @metabolite.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(@metabolite.errors.count, "error") %> prohibited this metabolite from being saved:</h2>

      <ul>
      <% @metabolite.errors.full_messages.each do |msg| %>
        <li><%= msg %></li>
      <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= f.label :name %><br />
    <%= f.text_field :name %>
  </div>
  <div class="field">
    <%= f.label :inchi %><br />
    <%= f.text_field :inchi %>
  </div>
  <div class="actions">
    <%= f.submit %>
  </div>
<% end %>
