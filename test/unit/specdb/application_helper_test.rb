require 'test_helper'

module Specdb
  class ApplicationHelperTest < ActiveSupport::TestCase
    class ApplicationHelperView < ActionView::Base
      include Specdb::ApplicationHelper
    end

    class ApplicationHelperTestSpec < Spectrum
      self.element_name    = "test_spec"
      self.collection_name = "test_spec"
    end

    setup do
      @mock_view  = ApplicationHelperView.new
      @test_spec1 = ApplicationHelperTestSpec.new(:id => 1)
      @test_spec2 = ApplicationHelperTestSpec.new(:id => 2)
    end

    test "data_url_for returns url to SpecDB data path" do 
      expected1 = "http://specdb.wishartlab.com/spectra/test_spec/1/data.json"
      expected2 = "http://specdb.wishartlab.com/spectra/test_spec/2/data.json"
      assert_equal expected1, @mock_view.data_url_for(@test_spec1)
      assert_equal expected2, @mock_view.data_url_for(@test_spec2)
    end


    test "spectrum_graph_for" do
      expected = "<div class='spectrum_graph test_spec' data='http://specdb.wishartlab.com/spectra/test_spec/1/data.json' ></div>"
      assert_equal expected, @mock_view.spectrum_graph_for(@test_spec1)
    end

  end
end


