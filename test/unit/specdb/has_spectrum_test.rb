require 'test_helper'

module Specdb
  class HasSpectraTest < ActiveSupport::TestCase

    class Compound < ActiveRecord::Base
      def self.columns() @columns ||= []; end  
      def inchi; end
      has_spectra
    end

    setup do
      @good_subject = Compound.new
    end

    test "should raise an error without inchi" do
      assert_raise do
        class BadCompound < ActiveRecord::Base
          def self.columns() @columns ||= []; end  
          has_spectra
        end
      end
    end


    test "add has_many for all spectrums" do
      assert @subject.respond_to?(:nmr_spectra)
      assert @subject.respond_to?(:ms_ms_spectra)
    end

  end
end
