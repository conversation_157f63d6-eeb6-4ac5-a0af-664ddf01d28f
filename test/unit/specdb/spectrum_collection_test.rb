require 'test_helper'

module Specdb
  class SpectrumCollectionTest < ActiveSupport::TestCase

    class TestSpec1 < Spectrum
      cached_resource.off!
    end

    class TestSpec2 < Spectrum
      cached_resource.off!
    end

    def setup
      @collection_name    = 'test_spec1'
      @spectra_collection = 'spectra'

      # attributes for our test spectrum
      @inchi = '1/C7H11N3O2/c1-10-3-5(9-4-10)2-6(8)7(11)12/h3-4,6H,2,8H2,1H3,(H,11,12)/t6-/m0/s1'
      @test_spec1  = TestSpec1.new( {:id => 1, :inchi => @inchi } )
      @test_msms = TestSpec2.new( {:id => 1, :inchi => '1/C7H11N3O2/c1-10-3-5' } )
      @spectrum_list =  [@test_msms, @test_spec1]
      @spectrum_hash =  {:test_spec2 => [@test_msms], :test_spec1 => [@test_spec1]}
      @spectra_collection = SpectrumCollection.new @spectrum_list
    end

    test "group spectrums by spectrum type" do
      grouped =  @spectra_collection.group_by_spectrum_type
      assert_equal @spectrum_hash[:test_spec1].first , grouped[:test_spec1].first
      assert_equal @spectrum_hash[:test_spec2].first , grouped[:test_spec2].first
      assert_not_nil grouped["test_spec1"]
    end
  end
end
