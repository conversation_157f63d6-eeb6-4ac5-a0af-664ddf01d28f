GIT
  remote: *****************:wishartlab/wishart.git
  revision: a4380d952c30ded23a971b500782a8c32f6a4997
  specs:
    wishart (3.6.8)
      autoprefixer-rails
      bootstrap-kaminari-views
      bootstrap-sass (~> 3.3.0)
      browser (~> 0.8.0)
      favicon_maker
      jquery-turbolinks
      rails (~> 4.2.0)
      rubyzip (>= 1.0.0)
      slim-rails

PATH
  remote: .
  specs:
    specdb (3.15.0)
      activeresource
      faraday
      spectrum_hash
      wishart (>= 3.5.0)

GEM
  remote: http://rubygems.org/
  specs:
    actionmailer (4.2.10)
      actionpack (= 4.2.10)
      actionview (= 4.2.10)
      activejob (= 4.2.10)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (4.2.10)
      actionview (= 4.2.10)
      activesupport (= 4.2.10)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (4.2.10)
      activesupport (= 4.2.10)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (4.2.10)
      activesupport (= 4.2.10)
      globalid (>= 0.3.0)
    activemodel (4.2.10)
      activesupport (= 4.2.10)
      builder (~> 3.1)
    activerecord (4.2.10)
      activemodel (= 4.2.10)
      activesupport (= 4.2.10)
      arel (~> 6.0)
    activeresource (4.1.0)
      activemodel (~> 4.0)
      activesupport (~> 4.0)
      rails-observers (~> 0.1.2)
    activesupport (4.2.10)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    arel (6.0.4)
    autoprefixer-rails (7.1.6)
      execjs
    bootstrap-kaminari-views (0.0.5)
      kaminari (>= 0.13)
      rails (>= 3.1)
    bootstrap-sass (3.3.7)
      autoprefixer-rails (>= 5.2.1)
      sass (>= 3.3.4)
    browser (0.8.0)
    builder (3.2.3)
    concurrent-ruby (1.0.5)
    crass (1.0.3)
    docile (1.1.5)
    erubis (2.7.0)
    execjs (2.7.0)
    faraday (0.15.0)
      multipart-post (>= 1.2, < 3)
    favicon_maker (1.3.1)
      docile (~> 1.1)
    ffi (1.9.18)
    globalid (0.4.1)
      activesupport (>= 4.2.0)
    growl (1.0.3)
    guard-compat (1.2.1)
    guard-minitest (2.4.6)
      guard-compat (~> 1.2)
      minitest (>= 3.0)
    httparty (0.16.2)
      multi_xml (>= 0.5.2)
    i18n (0.9.1)
      concurrent-ruby (~> 1.0)
    jquery-rails (4.3.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-turbolinks (2.1.0)
      railties (>= 3.1.0)
      turbolinks
    kaminari (1.1.1)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.1.1)
      kaminari-activerecord (= 1.1.1)
      kaminari-core (= 1.1.1)
    kaminari-actionview (1.1.1)
      actionview
      kaminari-core (= 1.1.1)
    kaminari-activerecord (1.1.1)
      activerecord
      kaminari-core (= 1.1.1)
    kaminari-core (1.1.1)
    loofah (2.1.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.0)
      mini_mime (>= 0.1.1)
    mini_mime (1.0.0)
    mini_portile2 (2.3.0)
    minitest (5.10.3)
    multi_xml (0.6.0)
    multipart-post (2.0.0)
    nokogiri (1.8.1)
      mini_portile2 (~> 2.3.0)
    rack (1.6.8)
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (4.2.10)
      actionmailer (= 4.2.10)
      actionpack (= 4.2.10)
      actionview (= 4.2.10)
      activejob (= 4.2.10)
      activemodel (= 4.2.10)
      activerecord (= 4.2.10)
      activesupport (= 4.2.10)
      bundler (>= 1.3.0, < 2.0)
      railties (= 4.2.10)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.3)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.8)
      activesupport (>= 4.2.0.beta, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.0.3)
      loofah (~> 2.0)
    rails-observers (0.1.5)
      activemodel (>= 4.0)
    railties (4.2.10)
      actionpack (= 4.2.10)
      activesupport (= 4.2.10)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (12.3.0)
    rb-fsevent (0.10.2)
    rb-inotify (0.9.10)
      ffi (>= 0.5.0, < 2)
    rubyzip (1.2.1)
    sass (3.5.3)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    slim (3.0.9)
      temple (>= 0.7.6, < 0.9)
      tilt (>= 1.3.3, < 2.1)
    slim-rails (3.1.3)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (~> 3.0)
    spectrum_hash (0.2.1)
      httparty
    sprockets (3.7.1)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.1)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sqlite3 (1.3.13)
    temple (0.8.0)
    thor (0.20.0)
    thread_safe (0.3.6)
    tilt (2.0.8)
    turbolinks (5.0.1)
      turbolinks-source (~> 5)
    turbolinks-source (5.0.3)
    tzinfo (1.2.4)
      thread_safe (~> 0.1)

PLATFORMS
  ruby

DEPENDENCIES
  bootstrap-sass
  growl
  guard-minitest
  jquery-rails
  rb-fsevent
  rb-inotify
  specdb!
  sqlite3
  wishart!

BUNDLED WITH
   1.16.1
