# Peaklist File Upload Feature

## Overview
Added the ability for users to upload a peaklist .txt file as an alternative to pasting their peaklist in the query_masses textbox on the MS search form.

## Features Added

### 1. File Upload Interface
- Added a "Choose File" button next to the query masses textarea
- File input accepts only .txt files
- Shows selected filename after file selection
- "Clear" button to remove selected file and clear textarea

### 2. File Validation
- **File Type**: Only .txt files are accepted
- **File Size**: Maximum 1MB file size limit
- **Line Count**: Maximum 700 lines (matches LIMIT_PEAK_COUNT from controller)
- **Format Validation**: Each line should contain:
  - A mass value (required)
  - An optional CCS value (separated by whitespace)
  - Maximum 2 values per line

### 3. User Experience
- Real-time validation with user-friendly error messages
- File content automatically populates the textarea when valid
- Manual typing in textarea clears file upload display
- Responsive design that works on mobile devices

### 4. Backend Processing
- Controller handles both file upload and textarea input seamlessly
- Same validation logic applied to both input methods
- Proper error handling with flash messages
- Works with existing CSV export functionality

## Usage

### For Users
1. Navigate to the MS Search page
2. Either:
   - Type/paste masses directly in the textarea, OR
   - Click "Choose File" and select a .txt file with your peaklist
3. File format should be one mass (and optional CCS) per line:
   ```
   175.119 150.2
   147.044 120.5
   119.049 180.3
   91.054
   77.039 110.8
   ```
4. Submit the form as usual

### File Format Requirements
- Plain text file (.txt extension)
- One entry per line
- Each line format: `mass [ccs_value]`
- Mass value is required (numeric)
- CCS value is optional (numeric, separated by whitespace)
- Maximum 700 lines
- Maximum file size: 1MB

## Technical Implementation

### Files Modified/Added
1. **app/views/specdb/ms/search.html.slim** - Added file upload UI
2. **app/controllers/specdb/ms_controller.rb** - Added file processing logic
3. **app/assets/javascripts/specdb/file_upload.coffee** - Client-side validation and UX
4. **app/assets/stylesheets/specdb/file_upload.css** - Styling for upload interface
5. **app/assets/javascripts/specdb/application.js** - Include new JS file

### Key Features
- Maintains backward compatibility with existing textarea input
- Comprehensive client-side and server-side validation
- Error handling with user-friendly messages
- Responsive design
- Follows existing code patterns and conventions

## Testing
A sample test file `test_peaklist.txt` has been created to test the functionality with valid peaklist data.
